class BlackjackGame {
    constructor(canvas) {
        this.canvas = canvas;
        this.renderEngine = new RenderEngine(canvas);
        this.soundManager = new SoundManager();
        this.gameLogic = new BlackjackLogic();
        this.animationManager = new AnimationManager();
        this.uiManager = new UIManager(this.renderEngine, this.gameLogic);
        
        this.cards = [];
        this.chips = [];
        this.effects = [];
        
        this.mouseX = 0;
        this.mouseY = 0;
        this.isAnimating = false;
        
        this.setupEventListeners();
        this.setupGameObjects();
        this.renderEngine.addToLayer('ui', this.uiManager);
        
        this.renderEngine.start();
    }

    setupEventListeners() {
        this.canvas.addEventListener('mousemove', (e) => {
            const pos = Utils.getCanvasMousePos(this.canvas, e);
            this.mouseX = pos.x;
            this.mouseY = pos.y;
            this.handleMouseMove(pos.x, pos.y);
        });

        this.canvas.addEventListener('click', (e) => {
            const pos = Utils.getCanvasMousePos(this.canvas, e);
            this.handleClick(pos.x, pos.y);
        });

        this.canvas.addEventListener('mouseleave', () => {
            this.clearHovers();
        });

        window.addEventListener('resize', () => {
            this.handleResize();
        });

        // Resume audio context on first user interaction
        this.canvas.addEventListener('click', () => {
            this.soundManager.resumeContext();
        }, { once: true });
    }

    setupGameObjects() {
        this.createDeck();
        this.createChips();
    }

    createDeck() {
        // Create visual deck representation
        const deckX = 50;
        const deckY = this.renderEngine.getHeight() / 2 - 90;

        for (let i = 0; i < 5; i++) {
            const card = new Card('spades', 'A', deckX + i * 0.5, deckY + i * 0.5);
            card.faceUp = false;
            card.visible = true;
            this.cards.push(card);
            this.renderEngine.addToLayer('cards', card);
        }
    }

    createChips() {
        const chipValues = [5, 10, 25, 50, 100];
        const chipSpacing = 80;
        const startX = this.renderEngine.getWidth() / 2 - (chipSpacing * (chipValues.length - 1)) / 2;
        const y = this.renderEngine.getHeight() - 160;
        
        chipValues.forEach((value, index) => {
            const chip = new Chip(value, startX + index * chipSpacing, y);
            this.chips.push(chip);
            this.renderEngine.addToLayer('chips', chip);
        });
    }

    async dealInitialCards() {
        if (this.isAnimating) return;
        
        this.isAnimating = true;
        this.soundManager.play('deal');
        
        // Clear previous cards
        this.clearHandCards();
        
        // Deal player cards
        await this.dealCardToPlayer(true, 0);
        await Utils.sleep(300);
        
        // Deal dealer card (face up)
        await this.dealCardToDealer(true, 300);
        await Utils.sleep(300);
        
        // Deal second player card
        await this.dealCardToPlayer(true, 600);
        await Utils.sleep(300);
        
        // Deal dealer hole card (face down)
        await this.dealCardToDealer(false, 900);
        
        this.isAnimating = false;
        this.updateUI();
    }

    async dealCardToPlayer(faceUp = true, delay = 0) {
        const cardData = this.gameLogic.playerHand[this.gameLogic.playerHand.length - 1];
        if (!cardData) return;

        const deckX = 50;
        const deckY = this.renderEngine.getHeight() / 2 - 90;
        const card = new Card(cardData.suit, cardData.rank, deckX, deckY);
        card.faceUp = faceUp;

        
        const targetX = this.renderEngine.getWidth() / 2 + (this.gameLogic.playerHand.length - 1) * 90 - 45;
        const targetY = this.renderEngine.getHeight() - 220;
        
        this.renderEngine.addToLayer('cards', card);
        
        const animation = new CardDealAnimation(card, targetX, targetY, delay);
        this.animationManager.add(animation);
        
        this.soundManager.play('cardDeal');
        
        return new Promise(resolve => {
            setTimeout(resolve, 800 + delay);
        });
    }

    async dealCardToDealer(faceUp = true, delay = 0) {
        const cardData = this.gameLogic.dealerHand[this.gameLogic.dealerHand.length - 1];
        if (!cardData) return;

        const deckX = 50;
        const deckY = this.renderEngine.getHeight() / 2 - 90;
        const card = new Card(cardData.suit, cardData.rank, deckX, deckY);
        card.faceUp = faceUp;
        
        const targetX = this.renderEngine.getWidth() / 2 + (this.gameLogic.dealerHand.length - 1) * 90 - 45;
        const targetY = 80;
        
        this.renderEngine.addToLayer('cards', card);
        
        const animation = new CardDealAnimation(card, targetX, targetY, delay);
        this.animationManager.add(animation);
        
        this.soundManager.play('cardDeal');
        
        return new Promise(resolve => {
            setTimeout(resolve, 800 + delay);
        });
    }

    async flipDealerHoleCard() {
        const dealerCards = this.renderEngine.layers.get('cards').objects.filter(card => 
            card.y < this.renderEngine.getHeight() / 2
        );
        
        if (dealerCards.length >= 2) {
            const holeCard = dealerCards[1];
            await holeCard.flip();
            this.soundManager.play('cardFlip');
        }
    }

    async dealerDrawCards() {
        while (this.gameLogic.getHandValue(this.gameLogic.dealerHand) < 17) {
            await Utils.sleep(800);
            await this.dealCardToDealer(true);
        }
    }

    clearHandCards() {
        const cardLayer = this.renderEngine.layers.get('cards');
        cardLayer.objects = cardLayer.objects.filter(card => {
            // Keep deck cards, remove hand cards
            return card.x < 100;
        });
    }

    async showGameResult() {
        const result = this.gameLogic.gameResult;
        let message = this.gameLogic.getResultMessage();
        
        switch (result) {
            case 'blackjack':
                this.soundManager.play('blackjack');
                message = 'BLACKJACK!';
                break;
            case 'win':
            case 'dealerBust':
                this.soundManager.play('win');
                message = 'YOU WIN!';
                break;
            case 'bust':
            case 'lose':
            case 'dealerBlackjack':
                this.soundManager.play('lose');
                message = 'YOU LOSE!';
                break;
            case 'push':
                this.soundManager.play('push');
                message = 'PUSH!';
                break;
        }
        
        if (['blackjack', 'win', 'dealerBust'].includes(result)) {
            const winEffect = new WinEffect(
                this.renderEngine.getWidth() / 2,
                this.renderEngine.getHeight() / 2,
                message
            );
            this.renderEngine.addToLayer('effects', winEffect);
        }
    }

    async animateChipBet(chipValue) {
        const chip = this.chips.find(c => c.value === chipValue);
        if (!chip) return;
        
        const newChip = new Chip(chipValue, chip.x, chip.y);
        const targetX = this.renderEngine.getWidth() / 2;
        const targetY = this.renderEngine.getHeight() / 2 + 100;
        
        this.renderEngine.addToLayer('chips', newChip);
        
        const animation = new ChipThrowAnimation(newChip, targetX, targetY);
        this.animationManager.add(animation);
        
        this.soundManager.play('chipPlace');
        
        return new Promise(resolve => {
            setTimeout(resolve, 600);
        });
    }

    handleClick(x, y) {
        this.uiManager.handleClick(x, y);
        
        // Handle game logic based on UI interactions
        const prevGameState = this.gameLogic.gameState;
        
        // Update UI button states
        this.uiManager.updateButtonStates();
        
        // Handle game state changes
        if (this.gameLogic.gameState === 'dealing' && prevGameState === 'betting') {
            this.dealInitialCards();
        } else if (this.gameLogic.gameState === 'dealerTurn' && prevGameState === 'playing') {
            this.handleDealerTurn();
        } else if (this.gameLogic.gameState === 'gameOver' && prevGameState !== 'gameOver') {
            this.handleGameEnd();
        }
    }

    async handleDealerTurn() {
        await this.flipDealerHoleCard();
        await Utils.sleep(500);
        await this.dealerDrawCards();
        await this.showGameResult();
    }

    async handleGameEnd() {
        await this.showGameResult();
    }

    handleMouseMove(x, y) {
        this.uiManager.handleMouseMove(x, y);
        
        // Handle card hover effects
        const cardLayer = this.renderEngine.layers.get('cards');
        cardLayer.objects.forEach(card => {
            const wasHovered = card.hovered;
            card.hovered = card.isPointInside(x, y);
            
            if (card.hovered && !wasHovered) {
                // Card hover sound could be added here
            }
        });
        
        // Handle chip hover effects
        this.chips.forEach(chip => {
            chip.setHovered(chip.isPointInside(x, y));
        });
    }

    clearHovers() {
        const cardLayer = this.renderEngine.layers.get('cards');
        cardLayer.objects.forEach(card => {
            card.setHovered(false);
        });
        
        this.chips.forEach(chip => {
            chip.setHovered(false);
        });
        
        this.uiManager.hoveredButton = null;
        this.uiManager.buttons.forEach(button => {
            button.hovered = false;
        });
    }

    updateUI() {
        this.uiManager.updateButtonStates();
    }

    handleResize() {
        this.renderEngine.resize();
        this.uiManager.width = this.renderEngine.getWidth();
        this.uiManager.height = this.renderEngine.getHeight();
        this.uiManager.createUI(); // Recreate UI with new dimensions
    }

    // Public API methods
    placeBet(amount) {
        if (this.gameLogic.placeBet(amount)) {
            this.animateChipBet(amount);
            this.updateUI();
            return true;
        }
        return false;
    }

    clearBet() {
        this.gameLogic.clearBet();
        this.updateUI();
    }

    hit() {
        if (this.gameLogic.hit()) {
            this.dealCardToPlayer(true);
            this.updateUI();
            
            if (this.gameLogic.gameState === 'gameOver') {
                setTimeout(() => this.handleGameEnd(), 1000);
            }
        }
    }

    stand() {
        if (this.gameLogic.stand()) {
            this.handleDealerTurn();
        }
    }

    doubleDown() {
        if (this.gameLogic.doubleDown()) {
            this.dealCardToPlayer(true);
            setTimeout(() => this.handleDealerTurn(), 1000);
        }
    }

    newGame() {
        this.gameLogic.newGame();
        this.clearHandCards();
        this.renderEngine.clearLayer('effects');
        this.updateUI();
    }

    // Getters for external access
    getGameState() {
        return this.gameLogic.gameState;
    }

    getBalance() {
        return this.gameLogic.balance;
    }

    getCurrentBet() {
        return this.gameLogic.currentBet;
    }

    getStats() {
        return this.gameLogic.stats;
    }
}
