$(document).ready(function() {
    $('body').css('font-family', '-apple-system, BlinkMacSystemFont, "Segoe UI", "Microsoft YaHei", Roboto, "Helvetica Neue", Arial, sans-serif');

    let effectsMuted = false;
    let effectsVolume = 0.5;
    let dealSound = null;
    let debugMode = false;

    // Audio setup
    try {
        dealSound = new Audio('audio/deal.mp3');
        dealSound.preload = 'auto';
        dealSound.volume = effectsVolume;
    } catch (error) {
        console.warn('Failed to load audio:', error);
    }

    // Timer management
    const activeTimers = {
        timeouts: new Set(),
        intervals: new Set(),
        animationFrames: new Set()
    };

    function safeSetTimeout(callback, delay) {
        const id = setTimeout(() => {
            activeTimers.timeouts.delete(id);
            callback();
        }, delay);
        activeTimers.timeouts.add(id);
        return id;
    }

    function safeSetInterval(callback, delay) {
        const id = setInterval(callback, delay);
        activeTimers.intervals.add(id);
        return id;
    }

    function clearAllTimers() {
        activeTimers.timeouts.forEach(id => clearTimeout(id));
        activeTimers.intervals.forEach(id => clearInterval(id));
        activeTimers.animationFrames.forEach(id => cancelAnimationFrame(id));
        activeTimers.timeouts.clear();
        activeTimers.intervals.clear();
        activeTimers.animationFrames.clear();
    }

    // DOM cache
    const domCache = {};
    function initDOMCache() {
        domCache.$gameStatus = $('#game-status');
        domCache.$dealCards = $('#deal-cards');
        domCache.$deckCount = $('#deck-count');
        domCache.$rulesModal = $('#rules-modal');
    }

    // Game settings
    let gameSettings = {
        playerCount: 1,
        deckCount: 6,
        pendingDeckCount: null
    };

    function getPlayerHtmlPosition(playerIndex) {
        if (gameSettings.playerCount === 1) {
            return 0;
        } else if (gameSettings.playerCount === 3) {
            return [0, 1, 2][playerIndex];
        } else if (gameSettings.playerCount === 4) {
            return [0, 1, 2, 3][playerIndex];
        } else if (gameSettings.playerCount === 6) {
            return [0, 1, 2, 3, 4, 5][playerIndex];
        }
        return playerIndex;
    }

    // Game state
    let gameState = {
        deck: [],
        discardPile: [],
        totalCards: 0,
        dealerCards: [],
        dealerScore: 0,
        players: [],
        currentPlayerIndex: 0,
        balance: 1000,
        balanceCache: 1000,
        gameInProgress: false,
        dealerHiddenCard: null,
        gameHistory: [],
        soundEnabled: true,
        currentTurnIndex: -1,
        gamePhase: 'waiting',
        gameStarted: false,
        isShuffling: false,
        insuranceOffered: false,
        dealerHasBlackjack: false,
        hasAutoFullscreened: false,
        gameOverModalShown: false
    };

    // Card definitions
    const suits = ['♠', '♥', '♦', '♣'];
    const values = ['A', '2', '3', '4', '5', '6', '7', '8', '9', '10', 'J', 'Q', 'K'];
    const suitColors = {'♠': 'black', '♣': 'black', '♥': 'red', '♦': 'red'};

    // Chip denominations
    const chipDenominations = [5, 10, 25, 50, 100, 500];

    // Initialize players
    function initializePlayers() {
        gameState.players = [];
        const humanPlayerIndex = 0;
        for (let i = 0; i < gameSettings.playerCount; i++) {
            const isHuman = i === humanPlayerIndex;
            const player = {
                cards: [],
                score: 0,
                bet: 0,
                isActive: false,
                isAI: !isHuman,
                name: 'You',
                isBust: false,
                avatar: 'images/user.png',
                splitHands: [],
                currentHandIndex: 0,
                canSplit: false,
                hasInsurance: false,
                insuranceBet: 0,
                hasSurrendered: false,
            };
            gameState.players.push(player);
        }
        gameState.currentPlayerIndex = humanPlayerIndex;
    }

    // Update player positions display
    function updatePlayerPositionsDisplay() {
        $('.player-position').hide();
        $('.players-area').removeClass('player-count-1 player-count-3 player-count-4 player-count-6');
        $('.players-area').addClass(`player-count-${gameSettings.playerCount}`);

        for (let i = 0; i < gameSettings.playerCount; i++) {
            const htmlPosition = getPlayerHtmlPosition(i);
            const playerPosition = $(`.player-position[data-position="${htmlPosition}"]`);
            if (playerPosition.length > 0) {
                playerPosition.show();
                const player = gameState.players[i];
                if (player) {
                    const avatarImg = playerPosition.find('.player-avatar img');
                    const playerName = playerPosition.find('.player-name');
                    if (avatarImg.length > 0) {
                        avatarImg.attr('src', player.avatar);
                        avatarImg.attr('alt', player.name);
                    }
                    if (playerName.length > 0) {
                        playerName.text(player.name);
                    }
                    playerPosition.addClass('current-player');
                    const playerBalance = playerPosition.find('.player-balance');
                    if (playerBalance.length > 0) {
                        playerBalance.show();
                    }
                }
            }
        }
    }

    // Create deck
    function createDeck() {
        gameState.deck = [];
        gameState.discardPile = [];

        for (let deckNum = 0; deckNum < gameSettings.deckCount; deckNum++) {
            for (let suit of suits) {
                for (let value of values) {
                    gameState.deck.push({
                        suit: suit,
                        value: value,
                        color: suitColors[suit]
                    });
                }
            }
        }

        gameState.totalCards = gameState.deck.length;
        console.log(`✅ Deck created: ${gameState.totalCards} cards (${gameSettings.deckCount} deck${gameSettings.deckCount > 1 ? 's' : ''})`);
        updateDeckDisplay();
    }

    // Shuffle deck
    function shuffleDeck() {
        for (let i = gameState.deck.length - 1; i > 0; i--) {
            const j = Math.floor(Math.random() * (i + 1));
            [gameState.deck[i], gameState.deck[j]] = [gameState.deck[j], gameState.deck[i]];
        }
        console.log('🔀 Deck shuffled');
    }

    // Update deck display
    function updateDeckDisplay() {
        const remainingCards = gameState.deck.length;
        const discardedCards = gameState.discardPile.length;
        const currentTotalCards = remainingCards + discardedCards;
        const baseTotalCards = gameState.totalCards;

        $('#deck-count').text(remainingCards);

        const deckProgress = baseTotalCards > 0 ? (remainingCards / baseTotalCards) * 100 : 0;
        $('#deck-progress-fill').css('width', `${deckProgress}%`);

        const deckCard = $('#main-deck .deck-card');
        if (remainingCards === 0) {
            deckCard.css('opacity', '0.3');
        } else if (remainingCards < baseTotalCards * 0.2) {
            deckCard.css('opacity', '0.6');
        } else {
            deckCard.css('opacity', '1');
        }
    }

    // Card value calculation
    function getCardValue(card) {
        if (card.value === 'A') {
            return 11;
        } else if (['J', 'Q', 'K'].includes(card.value)) {
            return 10;
        } else {
            return parseInt(card.value);
        }
    }

    // Calculate hand score
    function calculateScore(cards) {
        let score = 0;
        let aces = 0;

        for (let card of cards) {
            if (card.value === 'A') {
                aces++;
                score += 11;
            } else if (['J', 'Q', 'K'].includes(card.value)) {
                score += 10;
            } else {
                score += parseInt(card.value);
            }
        }

        while (score > 21 && aces > 0) {
            score -= 10;
            aces--;
        }

        return score;
    }

    // Check if hand is blackjack
    function isBlackjack(cards) {
        return cards.length === 2 && calculateScore(cards) === 21;
    }

    // Deal a card
    function dealCard() {
        if (gameState.deck.length === 0) {
            console.warn('⚠️ Deck is empty, reshuffling discard pile');
            gameState.deck = [...gameState.discardPile];
            gameState.discardPile = [];
            shuffleDeck();
        }

        if (gameState.deck.length === 0) {
            console.error('❌ No cards available to deal');
            return null;
        }

        const card = gameState.deck.pop();
        updateDeckDisplay();
        return card;
    }

    // Add card to player
    function addCardToPlayer(playerIndex, card) {
        if (!card) return;

        const player = gameState.players[playerIndex];
        player.cards.push(card);
        player.score = calculateScore(player.cards);
        player.isBust = player.score > 21;

        displayPlayerCard(playerIndex, card);
        updatePlayerScore(playerIndex);
    }

    // Add card to dealer
    function addCardToDealer(card, hidden = false) {
        if (!card) return;

        if (hidden) {
            gameState.dealerHiddenCard = card;
        } else {
            gameState.dealerCards.push(card);
            gameState.dealerScore = calculateScore(gameState.dealerCards);
        }

        displayDealerCard(card, hidden);
        if (!hidden) {
            updateDealerScore();
        }
    }

    // Display player card
    function displayPlayerCard(playerIndex, card) {
        const htmlPosition = getPlayerHtmlPosition(playerIndex);
        const cardsArea = $(`#player-cards-${htmlPosition}`);

        const cardElement = createCardElement(card);
        cardElement.addClass('card-deal');
        cardsArea.append(cardElement);

        playSound('deal');
    }

    // Display dealer card
    function displayDealerCard(card, hidden = false) {
        const cardsArea = $('#dealer-cards');

        const cardElement = createCardElement(card, hidden);
        cardElement.addClass('card-deal');
        cardsArea.append(cardElement);

        playSound('deal');
    }

    // Create card element
    function createCardElement(card, hidden = false) {
        const cardDiv = $('<div>').addClass('card');

        if (hidden) {
            cardDiv.addClass('card-back');
            cardDiv.attr('data-hidden', 'true');
        } else {
            cardDiv.addClass(card.color);
            cardDiv.html(`
                <div class="card-value">${card.value}</div>
                <div class="card-suit">${card.suit}</div>
            `);
        }

        return cardDiv;
    }

    // Update player score display
    function updatePlayerScore(playerIndex) {
        const htmlPosition = getPlayerHtmlPosition(playerIndex);
        const player = gameState.players[playerIndex];
        $(`#player-score-${htmlPosition}`).text(player.score);

        if (player.score > 0) {
            $(`.player-position[data-position="${htmlPosition}"] .player-score`).removeClass('hidden');
        }
    }

    // Update dealer score display
    function updateDealerScore() {
        $('#dealer-score').text(gameState.dealerScore);
    }

    // Play sound effect
    function playSound(soundName) {
        if (!gameState.soundEnabled || effectsMuted) return;

        try {
            if (soundName === 'deal' && dealSound) {
                dealSound.currentTime = 0;
                dealSound.play().catch(e => console.warn('Audio play failed:', e));
            }
        } catch (error) {
            console.warn('Sound play error:', error);
        }
    }

    // Update game status
    function updateGameStatus(message) {
        $('#game-status').text(message);
    }

    // Show game status
    function showGameStatus(message) {
        updateGameStatus(message);
        $('#game-status').fadeIn();
    }

    // Update chip denominations
    function updateChipDenominations() {
        const chipTray = $('.chip-tray');
        chipTray.empty();

        chipDenominations.forEach(value => {
            const chip = $('<div>')
                .addClass(`chip chip-${value}`)
                .attr('data-value', value)
                .text(value)
                .on('click', function() {
                    placeBet(value);
                });
            chipTray.append(chip);
        });
    }

    // Place bet
    function placeBet(amount) {
        if (gameState.gameInProgress || gameState.gamePhase !== 'betting') {
            return;
        }

        const currentPlayer = gameState.players[gameState.currentPlayerIndex];
        if (gameState.balance >= amount) {
            gameState.balance -= amount;
            currentPlayer.bet += amount;

            animateChipToBet(amount, gameState.currentPlayerIndex);
            updateDisplay();
            updateBetDisplay(gameState.currentPlayerIndex, currentPlayer.bet);
            updateChipButtonStates();
            updateButtonStates();
            playSound('chipPlace');

            if (currentPlayer.bet >= 5) {
                showDealButton();
            }
        } else {
            showGameStatus('Insufficient balance!');
        }
    }

    // Animate chip to bet area
    function animateChipToBet(amount, playerIndex) {
        const htmlPosition = getPlayerHtmlPosition(playerIndex);
        const betCircle = $(`.bet-circle[data-position="${htmlPosition}"]`);
        const chip = $(`.chip[data-value="${amount}"]`);

        if (chip.length && betCircle.length) {
            const flyingChip = chip.clone()
                .addClass('flying-chip')
                .css({
                    position: 'absolute',
                    left: chip.offset().left,
                    top: chip.offset().top,
                    zIndex: 1000
                });

            $('body').append(flyingChip);

            flyingChip.animate({
                left: betCircle.offset().left + betCircle.width() / 2 - flyingChip.width() / 2,
                top: betCircle.offset().top + betCircle.height() / 2 - flyingChip.height() / 2
            }, 500, function() {
                flyingChip.remove();
            });
        }
    }

    // Update bet display
    function updateBetDisplay(playerIndex, amount) {
        const htmlPosition = getPlayerHtmlPosition(playerIndex);
        $(`#bet-amount-${htmlPosition}`).text(amount);

        if (amount > 0) {
            $(`.bet-circle[data-position="${htmlPosition}"]`).addClass('active');
        } else {
            $(`.bet-circle[data-position="${htmlPosition}"]`).removeClass('active');
        }
    }

    // Update display
    function updateDisplay() {
        $('#player-balance').text(gameState.balance);
        updateDeckDisplay();
    }

    // Update chip button states
    function updateChipButtonStates() {
        $('.chip').each(function() {
            const chipValue = parseInt($(this).attr('data-value'));
            if (gameState.balance < chipValue) {
                $(this).addClass('disabled');
            } else {
                $(this).removeClass('disabled');
            }
        });
    }

    // Update button states
    function updateButtonStates() {
        const currentPlayer = gameState.players[gameState.currentPlayerIndex];
        const canPlay = gameState.gameInProgress && gameState.gamePhase === 'playing';

        $('#hit').prop('disabled', !canPlay);
        $('#stand').prop('disabled', !canPlay);
        $('#double-down').prop('disabled', !canPlay || !canDoubleDown());
        $('#split').prop('disabled', !canPlay || !canSplit());
        $('#surrender').prop('disabled', !canPlay || currentPlayer.cards.length !== 2);

        $('#clear-bet').prop('disabled', gameState.gameInProgress || currentPlayer.bet === 0);
        $('#double-bet').prop('disabled', gameState.gameInProgress || currentPlayer.bet === 0 || gameState.balance < currentPlayer.bet);
    }

    // Show deal button
    function showDealButton() {
        $('#deal-cards').prop('disabled', false).show();
    }

    // Hide deal button
    function hideDealButton() {
        $('#deal-cards').prop('disabled', true).hide();
    }

    // Clear bet
    function clearBet() {
        const currentPlayer = gameState.players[gameState.currentPlayerIndex];
        if (currentPlayer.bet > 0 && !gameState.gameInProgress) {
            gameState.balance += currentPlayer.bet;
            currentPlayer.bet = 0;
            updateDisplay();
            updateBetDisplay(gameState.currentPlayerIndex, 0);
            updateChipButtonStates();
            updateButtonStates();
            hideDealButton();
        }
    }

    // Double bet
    function doubleBet() {
        const currentPlayer = gameState.players[gameState.currentPlayerIndex];
        if (currentPlayer.bet > 0 && gameState.balance >= currentPlayer.bet && !gameState.gameInProgress) {
            const additionalBet = currentPlayer.bet;
            gameState.balance -= additionalBet;
            currentPlayer.bet += additionalBet;

            animateChipToBet(additionalBet, gameState.currentPlayerIndex);
            updateDisplay();
            updateBetDisplay(gameState.currentPlayerIndex, currentPlayer.bet);
            updateChipButtonStates();
            updateButtonStates();
            playSound('chipPlace');
        }
    }

    // Check if can double down
    function canDoubleDown() {
        const currentPlayer = gameState.players[gameState.currentPlayerIndex];
        return currentPlayer.cards.length === 2 && gameState.balance >= currentPlayer.bet;
    }

    // Check if can split
    function canSplit() {
        const currentPlayer = gameState.players[gameState.currentPlayerIndex];
        return currentPlayer.cards.length === 2 &&
               currentPlayer.cards[0].value === currentPlayer.cards[1].value &&
               gameState.balance >= currentPlayer.bet &&
               currentPlayer.splitHands.length === 0;
    }

    // Start new game
    function startNewGame() {
        if (gameState.gameInProgress) return;

        const currentPlayer = gameState.players[gameState.currentPlayerIndex];
        if (currentPlayer.bet < 5) {
            showGameStatus('Minimum bet is $5');
            return;
        }

        gameState.gameInProgress = true;
        gameState.gamePhase = 'dealing';

        // Clear previous cards
        clearAllCards();

        // Reset player states
        gameState.players.forEach(player => {
            player.cards = [];
            player.score = 0;
            player.isBust = false;
            player.splitHands = [];
            player.currentHandIndex = 0;
            player.hasInsurance = false;
            player.insuranceBet = 0;
            player.hasSurrendered = false;
        });

        // Reset dealer
        gameState.dealerCards = [];
        gameState.dealerScore = 0;
        gameState.dealerHiddenCard = null;

        updateButtonStates();
        showGameStatus('Dealing cards...');

        // Deal initial cards
        safeSetTimeout(() => dealInitialCards(), 500);
    }

    // Clear all cards from display
    function clearAllCards() {
        $('#dealer-cards').empty();
        $('.player-cards-area').empty();
        $('.player-score').addClass('hidden');
        $('#dealer-score').text('0');
    }

    // Deal initial cards
    function dealInitialCards() {
        let dealIndex = 0;
        const totalDeals = 4; // 2 cards each for player and dealer

        function dealNextCard() {
            if (dealIndex >= totalDeals) {
                finishInitialDeal();
                return;
            }

            if (dealIndex % 2 === 0) {
                // Deal to player
                const card = dealCard();
                addCardToPlayer(gameState.currentPlayerIndex, card);
            } else {
                // Deal to dealer
                const card = dealCard();
                const isHidden = dealIndex === 3; // Second dealer card is hidden
                addCardToDealer(card, isHidden);
            }

            dealIndex++;
            safeSetTimeout(dealNextCard, 600);
        }

        dealNextCard();
    }

    // Finish initial deal
    function finishInitialDeal() {
        const currentPlayer = gameState.players[gameState.currentPlayerIndex];

        // Check for blackjacks
        const playerBlackjack = isBlackjack(currentPlayer.cards);
        const dealerUpCard = gameState.dealerCards[0];
        const dealerMightHaveBlackjack = dealerUpCard && (dealerUpCard.value === 'A' || getCardValue(dealerUpCard) === 10);

        if (playerBlackjack) {
            if (dealerMightHaveBlackjack) {
                // Check dealer blackjack
                revealDealerHiddenCard();
                safeSetTimeout(() => {
                    const dealerBlackjack = isBlackjack([...gameState.dealerCards, gameState.dealerHiddenCard]);
                    if (dealerBlackjack) {
                        endGame('push', 'Both have Blackjack - Push!');
                    } else {
                        endGame('blackjack', 'Blackjack! You win!');
                    }
                }, 1000);
            } else {
                endGame('blackjack', 'Blackjack! You win!');
            }
            return;
        }

        // Check for insurance
        if (dealerUpCard && dealerUpCard.value === 'A') {
            offerInsurance();
        } else {
            startPlayerTurn();
        }
    }

    // Offer insurance
    function offerInsurance() {
        const currentPlayer = gameState.players[gameState.currentPlayerIndex];
        const insuranceCost = Math.floor(currentPlayer.bet / 2);

        if (gameState.balance >= insuranceCost) {
            $('#insurance-cost').text(insuranceCost);
            $('#insurance-panel').fadeIn();
            gameState.insuranceOffered = true;
        } else {
            startPlayerTurn();
        }
    }

    // Start player turn
    function startPlayerTurn() {
        gameState.gamePhase = 'playing';
        updateButtonStates();
        showGameStatus('Your turn - Hit or Stand?');
    }

    // Player hit
    function hit() {
        if (gameState.gamePhase !== 'playing') return;

        const card = dealCard();
        addCardToPlayer(gameState.currentPlayerIndex, card);

        const currentPlayer = gameState.players[gameState.currentPlayerIndex];

        if (currentPlayer.score > 21) {
            safeSetTimeout(() => {
                showBustEffect(gameState.currentPlayerIndex);
                safeSetTimeout(() => {
                    endGame('bust', 'Bust! You lose.');
                }, 1000);
            }, 500);
        } else if (currentPlayer.score === 21) {
            safeSetTimeout(() => {
                stand();
            }, 500);
        } else {
            showGameStatus('Hit or Stand?');
            updateButtonStates();
        }
    }

    // Player stand
    function stand() {
        if (gameState.gamePhase !== 'playing') return;

        gameState.gamePhase = 'dealerTurn';
        updateButtonStates();
        showGameStatus('Dealer\'s turn...');

        safeSetTimeout(() => {
            dealerTurn();
        }, 1000);
    }

    // Show bust effect
    function showBustEffect(playerIndex) {
        const htmlPosition = getPlayerHtmlPosition(playerIndex);
        $(`.player-position[data-position="${htmlPosition}"]`).addClass('bust');
    }

    // Reveal dealer hidden card
    function revealDealerHiddenCard() {
        if (gameState.dealerHiddenCard) {
            gameState.dealerCards.push(gameState.dealerHiddenCard);
            gameState.dealerScore = calculateScore(gameState.dealerCards);
            gameState.dealerHiddenCard = null;

            // Update display
            const hiddenCard = $('#dealer-cards .card[data-hidden="true"]');
            if (hiddenCard.length) {
                hiddenCard.removeClass('card-back')
                    .removeAttr('data-hidden')
                    .addClass(gameState.dealerCards[gameState.dealerCards.length - 1].color)
                    .html(`
                        <div class="card-value">${gameState.dealerCards[gameState.dealerCards.length - 1].value}</div>
                        <div class="card-suit">${gameState.dealerCards[gameState.dealerCards.length - 1].suit}</div>
                    `)
                    .addClass('card-flip');
            }

            updateDealerScore();
        }
    }

    // Dealer turn
    function dealerTurn() {
        revealDealerHiddenCard();

        safeSetTimeout(() => {
            dealerDrawCards();
        }, 1000);
    }

    // Dealer draws cards
    function dealerDrawCards() {
        if (gameState.dealerScore < 17) {
            const card = dealCard();
            addCardToDealer(card);

            safeSetTimeout(() => {
                dealerDrawCards();
            }, 1000);
        } else {
            safeSetTimeout(() => {
                determineWinner();
            }, 1000);
        }
    }

    // Determine winner
    function determineWinner() {
        const currentPlayer = gameState.players[gameState.currentPlayerIndex];
        const playerScore = currentPlayer.score;
        const dealerScore = gameState.dealerScore;

        if (currentPlayer.isBust) {
            endGame('bust', 'You bust! Dealer wins.');
        } else if (dealerScore > 21) {
            endGame('win', 'Dealer busts! You win!');
        } else if (playerScore > dealerScore) {
            endGame('win', 'You win!');
        } else if (playerScore < dealerScore) {
            endGame('lose', 'Dealer wins.');
        } else {
            endGame('push', 'Push - It\'s a tie!');
        }
    }

    // End game
    function endGame(result, message) {
        gameState.gameInProgress = false;
        gameState.gamePhase = 'gameOver';

        const currentPlayer = gameState.players[gameState.currentPlayerIndex];
        let winnings = 0;

        switch (result) {
            case 'blackjack':
                winnings = Math.floor(currentPlayer.bet * 2.5);
                gameState.balance += winnings;
                break;
            case 'win':
                winnings = currentPlayer.bet * 2;
                gameState.balance += winnings;
                break;
            case 'push':
                winnings = currentPlayer.bet;
                gameState.balance += winnings;
                break;
            case 'lose':
            case 'bust':
                // No winnings
                break;
        }

        // Reset bet
        currentPlayer.bet = 0;
        updateBetDisplay(gameState.currentPlayerIndex, 0);

        updateDisplay();
        updateButtonStates();
        showGameStatus(message);

        // Check for game over
        safeSetTimeout(() => {
            if (gameState.balance < 5) {
                showGameOverModal();
            } else {
                // Prepare for next game
                safeSetTimeout(() => {
                    prepareNextGame();
                }, 2000);
            }
        }, 2000);
    }

    // Prepare next game
    function prepareNextGame() {
        gameState.gamePhase = 'betting';
        updateButtonStates();
        updateChipButtonStates();
        showGameStatus('Place your bet to start next game (Min: $5)');

        // Clear visual effects
        $('.player-position').removeClass('bust blackjack twenty-one');
    }

    // Show game over modal
    function showGameOverModal() {
        if (!gameState.gameOverModalShown) {
            gameState.gameOverModalShown = true;
            $('#game-over-modal').fadeIn();
        }
    }

    // Double down
    function doubleDown() {
        if (gameState.gamePhase !== 'playing') return;

        const currentPlayer = gameState.players[gameState.currentPlayerIndex];
        if (!canDoubleDown()) return;

        // Double the bet
        gameState.balance -= currentPlayer.bet;
        const originalBet = currentPlayer.bet;
        currentPlayer.bet *= 2;

        animateChipToBet(originalBet, gameState.currentPlayerIndex);
        updateDisplay();
        updateBetDisplay(gameState.currentPlayerIndex, currentPlayer.bet);

        // Deal one card and stand
        safeSetTimeout(() => {
            const card = dealCard();
            addCardToPlayer(gameState.currentPlayerIndex, card);

            safeSetTimeout(() => {
                if (currentPlayer.score > 21) {
                    showBustEffect(gameState.currentPlayerIndex);
                    safeSetTimeout(() => {
                        endGame('bust', 'Bust! You lose.');
                    }, 1000);
                } else {
                    stand();
                }
            }, 1000);
        }, 500);
    }

    // Split (simplified implementation)
    function split() {
        if (gameState.gamePhase !== 'playing') return;
        if (!canSplit()) return;

        // For now, just show a message
        showGameStatus('Split feature coming soon!');
    }

    // Surrender
    function surrender() {
        if (gameState.gamePhase !== 'playing') return;

        const currentPlayer = gameState.players[gameState.currentPlayerIndex];
        if (currentPlayer.cards.length !== 2) return;

        // Return half the bet
        const halfBet = Math.floor(currentPlayer.bet / 2);
        gameState.balance += halfBet;
        currentPlayer.bet = 0;

        updateDisplay();
        updateBetDisplay(gameState.currentPlayerIndex, 0);

        endGame('surrender', 'You surrendered. Half bet returned.');
    }

    // Insurance functions
    function buyInsurance() {
        const currentPlayer = gameState.players[gameState.currentPlayerIndex];
        const insuranceCost = Math.floor(currentPlayer.bet / 2);

        if (gameState.balance >= insuranceCost) {
            gameState.balance -= insuranceCost;
            currentPlayer.hasInsurance = true;
            currentPlayer.insuranceBet = insuranceCost;

            updateDisplay();
            $('#insurance-panel').fadeOut();

            // Check dealer blackjack
            const dealerBlackjack = isBlackjack([...gameState.dealerCards, gameState.dealerHiddenCard]);
            if (dealerBlackjack) {
                // Insurance pays 2:1
                gameState.balance += insuranceCost * 3;
                updateDisplay();
                showGameStatus('Dealer has Blackjack! Insurance pays 2:1');
            }

            startPlayerTurn();
        }
    }

    function declineInsurance() {
        $('#insurance-panel').fadeOut();
        startPlayerTurn();
    }

    // Event bindings
    function bindEvents() {
        // Game action buttons
        $('#hit').on('click', hit);
        $('#stand').on('click', stand);
        $('#double-down').on('click', doubleDown);
        $('#split').on('click', split);
        $('#surrender').on('click', surrender);

        // Betting buttons
        $('#clear-bet').on('click', clearBet);
        $('#double-bet').on('click', doubleBet);
        $('#deal-cards').on('click', startNewGame);

        // Insurance buttons
        $('#buy-insurance').on('click', buyInsurance);
        $('#decline-insurance').on('click', declineInsurance);

        // Modal buttons
        $('#restart-game-btn').on('click', function() {
            gameState.balance = 1000;
            gameState.gameOverModalShown = false;
            $('#game-over-modal').fadeOut();
            updateDisplay();
            prepareNextGame();
        });

        $('#skip-share-btn').on('click', function() {
            gameState.balance = 1000;
            gameState.gameOverModalShown = false;
            $('#share-incentive-modal').fadeOut();
            updateDisplay();
            prepareNextGame();
        });

        // Settings button
        $('#settings-button').on('click', function() {
            $('#settings-modal').fadeIn();
        });

        $('#settings-close').on('click', function() {
            $('#settings-modal').fadeOut();
        });

        // Rules button (if exists)
        $('#rules-button').on('click', function() {
            $('#rules-modal').fadeIn();
        });

        $('#rules-close').on('click', function() {
            $('#rules-modal').fadeOut();
        });

        // Settings modal
        $('#apply-settings-btn').on('click', function() {
            const newDeckCount = parseInt($('#deck-count-select').val());
            const newPlayerCount = parseInt($('#player-count-select').val());
            const soundEnabled = $('#sound-toggle').is(':checked');

            gameSettings.deckCount = newDeckCount;
            gameSettings.playerCount = newPlayerCount;
            gameState.soundEnabled = soundEnabled;

            // Restart game with new settings
            initGame();
            $('#settings-modal').fadeOut();
        });

        $('#reset-settings-btn').on('click', function() {
            $('#deck-count-select').val(6);
            $('#player-count-select').val(1);
            $('#sound-toggle').prop('checked', true);
        });

        // Hint button
        $('#hint').on('click', function() {
            showBasicStrategyHint();
        });

        // Share button
        $('#share-button').on('click', function() {
            if (navigator.share) {
                navigator.share({
                    title: 'Professional Blackjack Casino',
                    text: 'Play the best blackjack game online!',
                    url: window.location.href
                });
            } else {
                // Fallback for browsers without Web Share API
                const url = window.location.href;
                navigator.clipboard.writeText(url).then(() => {
                    showGameStatus('Link copied to clipboard!');
                });
            }
        });
    }

    // Show basic strategy hint
    function showBasicStrategyHint() {
        if (gameState.gamePhase !== 'playing') {
            showGameStatus('Hints are only available during your turn');
            return;
        }

        const currentPlayer = gameState.players[gameState.currentPlayerIndex];
        const playerScore = currentPlayer.score;
        const dealerUpCard = gameState.dealerCards[0];
        const dealerUpValue = getCardValue(dealerUpCard);

        let hint = '';

        // Basic strategy logic (simplified)
        if (playerScore <= 11) {
            hint = 'Basic strategy suggests: Hit';
        } else if (playerScore >= 17) {
            hint = 'Basic strategy suggests: Stand';
        } else if (playerScore === 12) {
            hint = dealerUpValue >= 4 && dealerUpValue <= 6 ? 'Basic strategy suggests: Stand' : 'Basic strategy suggests: Hit';
        } else if (playerScore >= 13 && playerScore <= 16) {
            hint = dealerUpValue <= 6 ? 'Basic strategy suggests: Stand' : 'Basic strategy suggests: Hit';
        } else {
            hint = 'Basic strategy suggests: Hit';
        }

        showGameStatus(hint);
    }

    // Initialize game
    function initGame() {
        initDOMCache();
        initializePlayers();
        createDeck();
        shuffleDeck();
        updateDisplay();
        updatePlayerPositionsDisplay();
        updateChipDenominations();
        bindEvents();
        startGame();
    }

    // Start game
    function startGame() {
        if (gameState.gameStarted) return;

        gameState.gameStarted = true;
        gameState.gamePhase = 'betting';
        updateButtonStates();
        updateChipButtonStates();
        hideDealButton();

        $('.dealer-section').addClass('show').show();
        $('.players-area').addClass('show').show();
        $('.top-status').show();
        $('.deck-area').show();

        showGameStatus('Welcome! Please place your bet to start playing (Min: $5)');
    }

    // Initialize when document is ready
    initGame();
});

    setupEventListeners() {
        this.canvas.addEventListener('mousemove', (e) => {
            const pos = Utils.getCanvasMousePos(this.canvas, e);
            this.mouseX = pos.x;
            this.mouseY = pos.y;
            this.handleMouseMove(pos.x, pos.y);
        });

        this.canvas.addEventListener('click', (e) => {
            const pos = Utils.getCanvasMousePos(this.canvas, e);
            this.handleClick(pos.x, pos.y);
        });

        this.canvas.addEventListener('mouseleave', () => {
            this.clearHovers();
        });

        window.addEventListener('resize', () => {
            this.handleResize();
        });

        // Resume audio context on first user interaction
        this.canvas.addEventListener('click', () => {
            this.soundManager.resumeContext();
        }, { once: true });
    }

    setupGameObjects() {
        this.createDeck();
        this.createChips();
    }

    createDeck() {
        // Create visual deck representation
        const deckX = 50;
        const deckY = this.renderEngine.getHeight() / 2 - 90;

        for (let i = 0; i < 5; i++) {
            const card = new Card('spades', 'A', deckX + i * 0.5, deckY + i * 0.5);
            card.faceUp = false;
            card.visible = true;
            this.cards.push(card);
            this.renderEngine.addToLayer('cards', card);
        }
    }

    createChips() {
        const chipValues = [5, 10, 25, 50, 100];
        const chipSpacing = 80;
        const startX = this.renderEngine.getWidth() / 2 - (chipSpacing * (chipValues.length - 1)) / 2;
        const y = this.renderEngine.getHeight() - 160;
        
        chipValues.forEach((value, index) => {
            const chip = new Chip(value, startX + index * chipSpacing, y);
            this.chips.push(chip);
            this.renderEngine.addToLayer('chips', chip);
        });
    }

    async dealInitialCards() {
        if (this.isAnimating) return;
        
        this.isAnimating = true;
        this.soundManager.play('deal');
        
        // Clear previous cards
        this.clearHandCards();
        
        // Deal player cards
        await this.dealCardToPlayer(true, 0);
        await Utils.sleep(300);
        
        // Deal dealer card (face up)
        await this.dealCardToDealer(true, 300);
        await Utils.sleep(300);
        
        // Deal second player card
        await this.dealCardToPlayer(true, 600);
        await Utils.sleep(300);
        
        // Deal dealer hole card (face down)
        await this.dealCardToDealer(false, 900);
        
        this.isAnimating = false;
        this.updateUI();
    }

    async dealCardToPlayer(faceUp = true, delay = 0) {
        const cardData = this.gameLogic.playerHand[this.gameLogic.playerHand.length - 1];
        if (!cardData) return;

        const deckX = 50;
        const deckY = this.renderEngine.getHeight() / 2 - 90;
        const card = new Card(cardData.suit, cardData.rank, deckX, deckY);
        card.faceUp = faceUp;

        
        const targetX = this.renderEngine.getWidth() / 2 + (this.gameLogic.playerHand.length - 1) * 90 - 45;
        const targetY = this.renderEngine.getHeight() - 220;
        
        this.renderEngine.addToLayer('cards', card);
        
        const animation = new CardDealAnimation(card, targetX, targetY, delay);
        this.animationManager.add(animation);
        
        this.soundManager.play('cardDeal');
        
        return new Promise(resolve => {
            setTimeout(resolve, 800 + delay);
        });
    }

    async dealCardToDealer(faceUp = true, delay = 0) {
        const cardData = this.gameLogic.dealerHand[this.gameLogic.dealerHand.length - 1];
        if (!cardData) return;

        const deckX = 50;
        const deckY = this.renderEngine.getHeight() / 2 - 90;
        const card = new Card(cardData.suit, cardData.rank, deckX, deckY);
        card.faceUp = faceUp;
        
        const targetX = this.renderEngine.getWidth() / 2 + (this.gameLogic.dealerHand.length - 1) * 90 - 45;
        const targetY = 80;
        
        this.renderEngine.addToLayer('cards', card);
        
        const animation = new CardDealAnimation(card, targetX, targetY, delay);
        this.animationManager.add(animation);
        
        this.soundManager.play('cardDeal');
        
        return new Promise(resolve => {
            setTimeout(resolve, 800 + delay);
        });
    }

    async flipDealerHoleCard() {
        const dealerCards = this.renderEngine.layers.get('cards').objects.filter(card => 
            card.y < this.renderEngine.getHeight() / 2
        );
        
        if (dealerCards.length >= 2) {
            const holeCard = dealerCards[1];
            await holeCard.flip();
            this.soundManager.play('cardFlip');
        }
    }

    async dealerDrawCards() {
        while (this.gameLogic.getHandValue(this.gameLogic.dealerHand) < 17) {
            await Utils.sleep(800);
            await this.dealCardToDealer(true);
        }
    }

    clearHandCards() {
        const cardLayer = this.renderEngine.layers.get('cards');
        cardLayer.objects = cardLayer.objects.filter(card => {
            // Keep deck cards, remove hand cards
            return card.x < 100;
        });
    }

    async showGameResult() {
        const result = this.gameLogic.gameResult;
        let message = this.gameLogic.getResultMessage();
        
        switch (result) {
            case 'blackjack':
                this.soundManager.play('blackjack');
                message = 'BLACKJACK!';
                break;
            case 'win':
            case 'dealerBust':
                this.soundManager.play('win');
                message = 'YOU WIN!';
                break;
            case 'bust':
            case 'lose':
            case 'dealerBlackjack':
                this.soundManager.play('lose');
                message = 'YOU LOSE!';
                break;
            case 'push':
                this.soundManager.play('push');
                message = 'PUSH!';
                break;
        }
        
        if (['blackjack', 'win', 'dealerBust'].includes(result)) {
            const winEffect = new WinEffect(
                this.renderEngine.getWidth() / 2,
                this.renderEngine.getHeight() / 2,
                message
            );
            this.renderEngine.addToLayer('effects', winEffect);
        }
    }

    async animateChipBet(chipValue) {
        const chip = this.chips.find(c => c.value === chipValue);
        if (!chip) return;
        
        const newChip = new Chip(chipValue, chip.x, chip.y);
        const targetX = this.renderEngine.getWidth() / 2;
        const targetY = this.renderEngine.getHeight() / 2 + 100;
        
        this.renderEngine.addToLayer('chips', newChip);
        
        const animation = new ChipThrowAnimation(newChip, targetX, targetY);
        this.animationManager.add(animation);
        
        this.soundManager.play('chipPlace');
        
        return new Promise(resolve => {
            setTimeout(resolve, 600);
        });
    }

    handleClick(x, y) {
        this.uiManager.handleClick(x, y);
        
        // Handle game logic based on UI interactions
        const prevGameState = this.gameLogic.gameState;
        
        // Update UI button states
        this.uiManager.updateButtonStates();
        
        // Handle game state changes
        if (this.gameLogic.gameState === 'dealing' && prevGameState === 'betting') {
            this.dealInitialCards();
        } else if (this.gameLogic.gameState === 'dealerTurn' && prevGameState === 'playing') {
            this.handleDealerTurn();
        } else if (this.gameLogic.gameState === 'gameOver' && prevGameState !== 'gameOver') {
            this.handleGameEnd();
        }
    }

    async handleDealerTurn() {
        await this.flipDealerHoleCard();
        await Utils.sleep(500);
        await this.dealerDrawCards();
        await this.showGameResult();
    }

    async handleGameEnd() {
        await this.showGameResult();
    }

    handleMouseMove(x, y) {
        this.uiManager.handleMouseMove(x, y);
        
        // Handle card hover effects
        const cardLayer = this.renderEngine.layers.get('cards');
        cardLayer.objects.forEach(card => {
            const wasHovered = card.hovered;
            card.hovered = card.isPointInside(x, y);
            
            if (card.hovered && !wasHovered) {
                // Card hover sound could be added here
            }
        });
        
        // Handle chip hover effects
        this.chips.forEach(chip => {
            chip.setHovered(chip.isPointInside(x, y));
        });
    }

    clearHovers() {
        const cardLayer = this.renderEngine.layers.get('cards');
        cardLayer.objects.forEach(card => {
            card.setHovered(false);
        });
        
        this.chips.forEach(chip => {
            chip.setHovered(false);
        });
        
        this.uiManager.hoveredButton = null;
        this.uiManager.buttons.forEach(button => {
            button.hovered = false;
        });
    }

    updateUI() {
        this.uiManager.updateButtonStates();
    }

    handleResize() {
        this.renderEngine.resize();
        this.uiManager.width = this.renderEngine.getWidth();
        this.uiManager.height = this.renderEngine.getHeight();
        this.uiManager.createUI(); // Recreate UI with new dimensions
    }

    // Public API methods
    placeBet(amount) {
        if (this.gameLogic.placeBet(amount)) {
            this.animateChipBet(amount);
            this.updateUI();
            return true;
        }
        return false;
    }

    clearBet() {
        this.gameLogic.clearBet();
        this.updateUI();
    }

    hit() {
        if (this.gameLogic.hit()) {
            this.dealCardToPlayer(true);
            this.updateUI();
            
            if (this.gameLogic.gameState === 'gameOver') {
                setTimeout(() => this.handleGameEnd(), 1000);
            }
        }
    }

    stand() {
        if (this.gameLogic.stand()) {
            this.handleDealerTurn();
        }
    }

    doubleDown() {
        if (this.gameLogic.doubleDown()) {
            this.dealCardToPlayer(true);
            setTimeout(() => this.handleDealerTurn(), 1000);
        }
    }

    newGame() {
        this.gameLogic.newGame();
        this.clearHandCards();
        this.renderEngine.clearLayer('effects');
        this.updateUI();
    }

    // Getters for external access
    getGameState() {
        return this.gameLogic.gameState;
    }

    getBalance() {
        return this.gameLogic.balance;
    }

    getCurrentBet() {
        return this.gameLogic.currentBet;
    }

    getStats() {
        return this.gameLogic.stats;
    }
}
