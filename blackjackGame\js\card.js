class Card extends GameObject {
    constructor(suit, rank, x = 0, y = 0) {
        super(x, y);
        this.suit = suit;
        this.rank = rank;
        this.width = 120;
        this.height = 180;
        this.faceUp = false;
        this.isFlipping = false;
        this.flipProgress = 0;
        this.flipSpeed = 0.15;
        
        this.targetX = x;
        this.targetY = y;
        this.targetZ = 0;
        this.z = 0;
        this.animating = false;
        this.animationSpeed = 0.12;
        
        this.hovered = false;
        this.selected = false;
        this.glowIntensity = 0;
        this.shadowIntensity = 1;
        
        this.velocity = { x: 0, y: 0, rotation: 0 };
        this.acceleration = { x: 0, y: 0, rotation: 0 };
        this.friction = 0.95;
        
        this.texture = null;
        this.backTexture = null;
        
        this.createTextures();
    }

    createTextures() {
        try {
            console.log('Creating card textures for', this.suit, this.rank);
            this.texture = this.createCardTexture();
            this.backTexture = this.createBackTexture();
            console.log('Card textures created successfully');
        } catch (error) {
            console.error('Error creating card textures:', error);
            // Create simple fallback textures
            this.texture = this.createSimpleTexture();
            this.backTexture = this.createSimpleTexture();
        }
    }

    createCardTexture() {
        const canvas = document.createElement('canvas');
        const ctx = canvas.getContext('2d');
        canvas.width = this.width;
        canvas.height = this.height;
        
        const gradient = ctx.createLinearGradient(0, 0, 0, this.height);
        gradient.addColorStop(0, '#ffffff');
        gradient.addColorStop(0.33, '#f8f8f8');
        gradient.addColorStop(0.66, '#f0f0f0');
        gradient.addColorStop(1, '#e8e8e8');
        
        ctx.fillStyle = gradient;
        ctx.fillRect(0, 0, this.width, this.height);

        const shadowGradient = ctx.createLinearGradient(0, 0, this.width, this.height);
        shadowGradient.addColorStop(0, 'rgba(0,0,0,0.1)');
        shadowGradient.addColorStop(1, 'rgba(0,0,0,0.3)');
        ctx.strokeStyle = shadowGradient;
        ctx.lineWidth = 2;
        ctx.strokeRect(1, 1, this.width - 2, this.height - 2);

        const borderGradient = ctx.createLinearGradient(0, 0, 0, this.height);
        borderGradient.addColorStop(0, '#d0d0d0');
        borderGradient.addColorStop(1, '#a0a0a0');
        ctx.strokeStyle = borderGradient;
        ctx.lineWidth = 1;
        ctx.strokeRect(0.5, 0.5, this.width - 1, this.height - 1);
        
        this.drawCardContent(ctx);
        
        return canvas;
    }

    drawCardContent(ctx) {
        const color = this.getSuitColor();
        const symbol = this.getSuitSymbol();
        
        ctx.font = 'bold 18px serif';
        ctx.fillStyle = color;
        ctx.textAlign = 'left';
        ctx.fillText(this.rank, 12, 20);

        ctx.font = '20px serif';
        ctx.fillText(symbol, 12, 45);
        
        ctx.save();
        ctx.translate(this.width, this.height);
        ctx.rotate(Math.PI);

        ctx.font = 'bold 18px serif';
        ctx.fillStyle = color;
        ctx.textAlign = 'left';
        ctx.fillText(this.rank, 12, 20);

        ctx.font = '20px serif';
        ctx.fillText(symbol, 12, 45);

        ctx.restore();

        ctx.font = '36px serif';
        ctx.fillStyle = color;
        ctx.textAlign = 'center';
        ctx.textBaseline = 'middle';
        ctx.fillText(symbol, this.width / 2, this.height / 2 + 12);
        
        if (this.rank === 'A') {
            this.drawAcePattern(ctx, color);
        } else if (['J', 'Q', 'K'].includes(this.rank)) {
            this.drawFacePattern(ctx, color);
        } else {
            this.drawNumberPattern(ctx, color, symbol);
        }
    }

    drawAcePattern(ctx, color) {
        ctx.strokeStyle = color;
        ctx.lineWidth = 3;
        ctx.beginPath();
        ctx.arc(this.width / 2, this.height / 2 + 40, 25, 0, Math.PI * 2);
        ctx.stroke();
    }

    drawFacePattern(ctx, color) {
        ctx.strokeStyle = color;
        ctx.lineWidth = 2;
        ctx.strokeRect(this.width / 2 - 30, this.height / 2 - 40, 60, 80);
    }

    drawNumberPattern(ctx, color, symbol) {
        const number = parseInt(this.rank) || 10;
        const positions = this.getSymbolPositions(number);
        
        ctx.font = '24px serif';
        ctx.fillStyle = color;
        ctx.textAlign = 'center';
        ctx.textBaseline = 'middle';

        positions.forEach(pos => {
            ctx.fillText(symbol, pos.x, pos.y);
        });
    }

    getSymbolPositions(number) {
        const positions = [];
        const centerX = this.width / 2;
        const centerY = this.height / 2;
        const offsetX = 25;
        const offsetY = 30;
        
        switch (number) {
            case 2:
                positions.push({ x: centerX, y: centerY - offsetY });
                positions.push({ x: centerX, y: centerY + offsetY });
                break;
            case 3:
                positions.push({ x: centerX, y: centerY - offsetY });
                positions.push({ x: centerX, y: centerY });
                positions.push({ x: centerX, y: centerY + offsetY });
                break;
            case 4:
                positions.push({ x: centerX - offsetX, y: centerY - offsetY });
                positions.push({ x: centerX + offsetX, y: centerY - offsetY });
                positions.push({ x: centerX - offsetX, y: centerY + offsetY });
                positions.push({ x: centerX + offsetX, y: centerY + offsetY });
                break;
            case 5:
                positions.push({ x: centerX - offsetX, y: centerY - offsetY });
                positions.push({ x: centerX + offsetX, y: centerY - offsetY });
                positions.push({ x: centerX, y: centerY });
                positions.push({ x: centerX - offsetX, y: centerY + offsetY });
                positions.push({ x: centerX + offsetX, y: centerY + offsetY });
                break;
            default:
                for (let i = 0; i < Math.min(number, 8); i++) {
                    const angle = (i * Math.PI * 2) / Math.min(number, 8);
                    positions.push({
                        x: centerX + Math.cos(angle) * offsetX,
                        y: centerY + Math.sin(angle) * offsetY
                    });
                }
        }
        
        return positions;
    }

    createBackTexture() {
        const canvas = document.createElement('canvas');
        const ctx = canvas.getContext('2d');
        canvas.width = this.width;
        canvas.height = this.height;
        
        const gradient = ctx.createRadialGradient(
            this.width / 2,
            this.height / 2,
            0,
            this.width / 2,
            this.height / 2,
            Math.max(this.width, this.height) / 2
        );
        gradient.addColorStop(0, '#1a237e');
        gradient.addColorStop(0.3, '#3949ab');
        gradient.addColorStop(0.7, '#303f9f');
        gradient.addColorStop(1, '#1a237e');
        
        ctx.fillStyle = gradient;
        ctx.fillRect(0, 0, this.width, this.height);

        ctx.strokeStyle = '#ffd700';
        ctx.lineWidth = 4;
        ctx.strokeRect(2, 2, this.width - 4, this.height - 4);
        
        this.drawBackPattern(ctx);
        
        return canvas;
    }

    drawBackPattern(ctx) {
        ctx.fillStyle = '#ffd700';

        for (let i = 0; i < 6; i++) {
            for (let j = 0; j < 9; j++) {
                const x = 15 + i * 18;
                const y = 20 + j * 18;

                ctx.beginPath();
                ctx.arc(x, y, 3, 0, Math.PI * 2);
                ctx.fill();
            }
        }

        ctx.strokeStyle = '#ffd700';
        ctx.lineWidth = 3;
        ctx.beginPath();
        ctx.arc(this.width / 2, this.height / 2, 40, 0, Math.PI * 2);
        ctx.stroke();

        ctx.beginPath();
        ctx.arc(this.width / 2, this.height / 2, 25, 0, Math.PI * 2);
        ctx.stroke();

        ctx.font = 'bold 16px serif';
        ctx.fillStyle = '#ffd700';
        ctx.textAlign = 'center';
        ctx.textBaseline = 'middle';
        ctx.fillText('♠♥♦♣', this.width / 2, this.height / 2 + 5);
    }

    createSimpleTexture() {
        const canvas = document.createElement('canvas');
        const ctx = canvas.getContext('2d');
        canvas.width = this.width;
        canvas.height = this.height;

        // Simple white rectangle with border
        ctx.fillStyle = '#ffffff';
        ctx.fillRect(0, 0, this.width, this.height);

        ctx.strokeStyle = '#000000';
        ctx.lineWidth = 2;
        ctx.strokeRect(1, 1, this.width - 2, this.height - 2);

        // Simple text
        ctx.fillStyle = '#000000';
        ctx.font = '16px Arial';
        ctx.textAlign = 'center';
        ctx.fillText(this.rank, this.width / 2, this.height / 2);

        return canvas;
    }

    getSuitColor() {
        return ['hearts', 'diamonds'].includes(this.suit) ? '#dc143c' : '#000000';
    }

    getSuitSymbol() {
        const symbols = {
            'hearts': '♥',
            'diamonds': '♦',
            'clubs': '♣',
            'spades': '♠'
        };
        return symbols[this.suit] || this.suit;
    }

    getValue() {
        if (this.rank === 'A') return 11;
        if (['K', 'Q', 'J'].includes(this.rank)) return 10;
        return parseInt(this.rank);
    }

    update(deltaTime) {
        super.update(deltaTime);
        
        if (this.animating) {
            const dx = this.targetX - this.x;
            const dy = this.targetY - this.y;
            const dz = this.targetZ - this.z;
            const distance = Math.sqrt(dx * dx + dy * dy + dz * dz);
            
            if (distance > 2) {
                const speed = this.animationSpeed * (deltaTime / 16.67);
                this.x += dx * speed;
                this.y += dy * speed;
                this.z += dz * speed;
            } else {
                this.x = this.targetX;
                this.y = this.targetY;
                this.z = this.targetZ;
                this.animating = false;
            }
        }
        
        this.velocity.x += this.acceleration.x;
        this.velocity.y += this.acceleration.y;
        this.velocity.rotation += this.acceleration.rotation;
        
        this.x += this.velocity.x;
        this.y += this.velocity.y;
        this.rotation += this.velocity.rotation;
        
        this.velocity.x *= this.friction;
        this.velocity.y *= this.friction;
        this.velocity.rotation *= this.friction;
        
        this.acceleration.x = 0;
        this.acceleration.y = 0;
        this.acceleration.rotation = 0;
        
        if (this.isFlipping) {
            this.flipProgress += this.flipSpeed;
            if (this.flipProgress >= 1) {
                this.flipProgress = 0;
                this.isFlipping = false;
                this.faceUp = !this.faceUp;
            }
        }
        
        if (this.hovered) {
            this.glowIntensity = Math.min(this.glowIntensity + 0.1, 1);
        } else {
            this.glowIntensity = Math.max(this.glowIntensity - 0.1, 0);
        }
    }

    draw(ctx, deltaTime) {
        this.drawShadow(ctx);
        
        if (this.glowIntensity > 0) {
            this.drawGlow(ctx);
        }
        
        if (this.isFlipping) {
            this.drawFlipping(ctx);
        } else {
            const texture = this.faceUp ? this.texture : this.backTexture;
            if (texture) {
                ctx.drawImage(texture, 0, 0, this.width, this.height);
            }
        }
        
        if (this.hovered) {
            ctx.translate(0, -5);
        }
    }

    drawShadow(ctx) {
        if (this.z > 0 || this.hovered) {
            ctx.save();
            ctx.globalAlpha = 0.3 * (this.hovered ? 1.5 : 1);
            ctx.fillStyle = '#000000';
            ctx.filter = `blur(${8 + this.z * 2}px)`;
            ctx.fillRect(
                this.x + 3 + this.z * 0.5,
                this.y + 3 + this.z * 0.5,
                this.width,
                this.height
            );
            ctx.restore();
        }
    }

    drawGlow(ctx) {
        ctx.save();
        ctx.globalAlpha = this.glowIntensity * 0.6;
        ctx.strokeStyle = '#ffd700';
        ctx.lineWidth = 8;
        ctx.filter = 'blur(4px)';
        ctx.strokeRect(this.x - 4, this.y - 4, this.width + 8, this.height + 8);
        ctx.restore();
    }

    drawFlipping(ctx) {
        const progress = this.flipProgress;
        const scaleX = Math.abs(Math.cos(progress * Math.PI));
        
        ctx.save();
        ctx.scale(scaleX, 1);
        
        const texture = progress < 0.5 ? this.backTexture : this.texture;
        if (texture) {
            ctx.drawImage(texture, 0, 0, this.width, this.height);
        }
        
        ctx.restore();
    }

    animateTo(x, y, z = 0, duration = 1000) {
        this.targetX = x;
        this.targetY = y;
        this.targetZ = z;
        this.animating = true;
        
        return new Promise(resolve => {
            setTimeout(() => {
                this.animating = false;
                resolve();
            }, duration);
        });
    }

    animateParabolic(targetX, targetY, height = 100, duration = 1000) {
        return new Promise(resolve => {
            const startX = this.x;
            const startY = this.y;
            const startTime = Date.now();
            
            const animate = () => {
                const elapsed = Date.now() - startTime;
                const progress = Math.min(elapsed / duration, 1);
                
                this.x = startX + (targetX - startX) * progress;
                
                const parabolicY = -4 * height * progress * (progress - 1);
                this.y = startY + (targetY - startY) * progress + parabolicY;
                
                this.rotation = progress * Math.PI * 2;
                
                if (progress < 1) {
                    requestAnimationFrame(animate);
                } else {
                    this.x = targetX;
                    this.y = targetY;
                    this.rotation = 0;
                    resolve();
                }
            };
            animate();
        });
    }

    flip(duration = 500) {
        return new Promise(resolve => {
            this.isFlipping = true;
            this.flipProgress = 0;
            this.flipSpeed = 1 / (duration / 16.67);
            
            setTimeout(() => {
                resolve();
            }, duration);
        });
    }

    setHovered(hovered) {
        this.hovered = hovered;
    }

    setSelected(selected) {
        this.selected = selected;
    }

    applyForce(forceX, forceY, torque = 0) {
        this.acceleration.x += forceX;
        this.acceleration.y += forceY;
        this.acceleration.rotation += torque;
    }
}
