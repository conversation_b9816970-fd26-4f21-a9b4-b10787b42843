* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Arial', sans-serif;
    background: #0a0a0a;
    min-height: 100vh;
    overflow: hidden;
    display: flex;
    justify-content: center;
    align-items: center;
}

.game-container {
    width: 100vw;
    height: 100vh;
    position: relative;
    display: flex;
    justify-content: center;
    align-items: center;
}

#game-canvas {
    width: 100%;
    height: 100%;
    max-width: 1400px;
    max-height: 900px;
    background: linear-gradient(135deg, #0f4c3a 0%, #1a5f4a 50%, #0f4c3a 100%);
    border-radius: 20px;
    box-shadow: 0 0 50px rgba(0, 0, 0, 0.8);
    cursor: pointer;
}

.loading-screen {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.9);
    display: flex;
    justify-content: center;
    align-items: center;
    z-index: 1000;
    transition: opacity 0.5s ease;
}

.loading-content {
    text-align: center;
    color: #ffd700;
}

.loading-content h2 {
    font-size: 2em;
    margin-bottom: 20px;
    text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.8);
}

.loading-bar {
    width: 300px;
    height: 10px;
    background: rgba(255, 255, 255, 0.2);
    border-radius: 5px;
    overflow: hidden;
    margin: 20px auto;
}

.loading-progress {
    height: 100%;
    background: linear-gradient(90deg, #ffd700, #ffed4e);
    width: 0%;
    transition: width 0.3s ease;
    border-radius: 5px;
}

#loading-text {
    font-size: 1.2em;
    margin-top: 15px;
    opacity: 0.8;
}

@media (max-width: 768px) {
    #game-canvas {
        border-radius: 0;
        max-width: 100vw;
        max-height: 100vh;
    }
}

@media (max-width: 480px) {
    .loading-content h2 {
        font-size: 1.5em;
    }

    .loading-bar {
        width: 250px;
    }

    #loading-text {
        font-size: 1em;
    }
}




