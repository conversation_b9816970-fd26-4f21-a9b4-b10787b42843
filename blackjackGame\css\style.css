/* Reset and Base Styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

html {
    width: 100vw;
    height: 100vh;
    height: 100dvh;
    overflow-x: hidden;
    margin: 0;
    padding: 0;
}

body {
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Microsoft YaHei', <PERSON><PERSON>, 'Helvetica Neue', Arial, sans-serif;
    background: linear-gradient(135deg, #0f4c3a 0%, #1a5f4a 50%, #0f4c3a 100%);
    color: #ffffff;
    min-height: 100vh;
    min-height: 100dvh;
    width: 100vw;
    overflow-x: hidden;
    margin: 0;
    padding: 0;
    background-attachment: fixed;
    background-size: cover !important;
    background-position: center center !important;
    position: relative;
}

body::before {
    content: '';
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    pointer-events: none;
    z-index: 1;
}

/* Main Container */
.casino-container {
    width: 100vw;
    height: 100dvh;
    position: relative;
    z-index: 2;
    display: flex;
    flex-direction: column;
}

/* Top Status Bar */
.top-status {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 60px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 10px 20px;
    z-index: 1000;
    background: rgba(0, 0, 0, 0.3);
    backdrop-filter: blur(10px);
}

.left-controls, .right-controls {
    display: flex;
    align-items: center;
    gap: 10px;
}

.settings-button {
    background: rgba(255, 255, 255, 0.1);
    border: 2px solid rgba(255, 255, 255, 0.2);
    border-radius: 12px;
    padding: 8px 12px;
    color: white;
    cursor: pointer;
    transition: all 0.3s ease;
    backdrop-filter: blur(10px);
    font-size: 16px;
    min-width: 44px;
    min-height: 44px;
    display: flex;
    align-items: center;
    justify-content: center;
}

.settings-button:hover {
    background: rgba(255, 255, 255, 0.2);
    border-color: rgba(255, 255, 255, 0.4);
    transform: translateY(-2px);
}

.settings-button:active {
    transform: translateY(0);
}

/* Deck Area */
.deck-area {
    position: absolute;
    top: 80px;
    right: 20px;
    z-index: 100;
}

.deck-container {
    display: flex;
    align-items: center;
    gap: 15px;
    background: rgba(0, 0, 0, 0.3);
    padding: 15px;
    border-radius: 15px;
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.1);
}

.deck-pile {
    display: flex;
    align-items: center;
    gap: 15px;
}

.deck-card {
    width: 50px;
    height: 70px;
    border-radius: 8px;
    position: relative;
    transition: opacity 0.3s ease;
}

.deck-info {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 5px;
}

.deck-label {
    font-size: 12px;
    color: rgba(255, 255, 255, 0.7);
    text-transform: uppercase;
    letter-spacing: 1px;
}

.deck-count {
    font-size: 18px;
    font-weight: bold;
    color: #ffffff;
}

.deck-progress {
    width: 60px;
    height: 4px;
    background: rgba(255, 255, 255, 0.2);
    border-radius: 2px;
    overflow: hidden;
}

.progress-bar {
    width: 100%;
    height: 100%;
    position: relative;
}

.progress-fill {
    height: 100%;
    background: linear-gradient(90deg, #10b981, #34d399);
    border-radius: 2px;
    transition: width 0.3s ease;
}

/* Casino Table */
.casino-table {
    flex: 1;
    display: flex;
    flex-direction: column;
    justify-content: space-between;
    padding: 100px 20px 200px;
    position: relative;
}

/* Dealer Section */
.dealer-section {
    display: flex;
    flex-direction: column;
    align-items: center;
    margin-bottom: 40px;
}

.dealer-info {
    display: flex;
    align-items: center;
    gap: 20px;
    margin-bottom: 20px;
    background: rgba(0, 0, 0, 0.3);
    padding: 10px 20px;
    border-radius: 25px;
    backdrop-filter: blur(10px);
}

.dealer-title {
    font-size: 18px;
    font-weight: bold;
    color: #ffffff;
}

.dealer-score {
    background: rgba(255, 255, 255, 0.1);
    padding: 5px 15px;
    border-radius: 15px;
    font-size: 16px;
    font-weight: bold;
    color: #ffffff;
    min-width: 40px;
    text-align: center;
}

.dealer-cards-container {
    min-height: 100px;
    display: flex;
    align-items: center;
    justify-content: center;
}

.dealer-cards-area {
    display: flex;
    gap: -20px;
    align-items: center;
    justify-content: center;
    min-height: 100px;
}

/* Players Area */
.players-area {
    display: flex;
    justify-content: center;
    align-items: center;
    gap: 20px;
    margin-top: 40px;
}

.player-position {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 15px;
    min-width: 200px;
    opacity: 0.6;
    transition: opacity 0.3s ease;
}

.player-position.current-player {
    opacity: 1;
}

.player-cards-container {
    position: relative;
    min-height: 100px;
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 10px;
}

.player-cards-area {
    display: flex;
    gap: -20px;
    align-items: center;
    justify-content: center;
    min-height: 100px;
}

.player-score {
    background: rgba(255, 255, 255, 0.1);
    padding: 5px 15px;
    border-radius: 15px;
    font-size: 14px;
    font-weight: bold;
    color: #ffffff;
    backdrop-filter: blur(10px);
    transition: opacity 0.3s ease;
}

.player-score.hidden {
    opacity: 0;
}

/* Bet Spots */
.bet-spot {
    display: flex;
    justify-content: center;
    align-items: center;
}

.bet-circle {
    width: 80px;
    height: 80px;
    border: 3px solid rgba(255, 255, 255, 0.3);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    background: rgba(0, 0, 0, 0.2);
    backdrop-filter: blur(10px);
    transition: all 0.3s ease;
    position: relative;
}

.bet-circle.active {
    border-color: #10b981;
    box-shadow: 0 0 20px rgba(16, 185, 129, 0.3);
}

.bet-amount {
    font-size: 16px;
    font-weight: bold;
    color: #ffffff;
}

/* Table Center */
.table-center {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    z-index: 10;
}

.game-status {
    background: rgba(0, 0, 0, 0.8);
    color: #ffffff;
    padding: 15px 30px;
    border-radius: 25px;
    font-size: 16px;
    font-weight: 500;
    text-align: center;
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.1);
    max-width: 400px;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
}

/* Card Styles */
.card {
    width: 60px;
    height: 84px;
    background: #ffffff;
    border-radius: 8px;
    border: 1px solid #ddd;
    display: flex;
    flex-direction: column;
    justify-content: space-between;
    padding: 4px;
    margin: 0 -15px;
    position: relative;
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.3);
    transition: all 0.3s ease;
    z-index: 1;
}

.card:hover {
    transform: translateY(-5px);
    z-index: 2;
}

.card.red {
    color: #dc2626;
}

.card.black {
    color: #1f2937;
}

.card-back {
    background: linear-gradient(145deg, #1e40af, #3b82f6);
    position: relative;
    overflow: hidden;
    color: transparent;
}

.card-back::before {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    width: 40px;
    height: 40px;
    background: repeating-linear-gradient(45deg,
            #ffffff20,
            #ffffff20 2px,
            transparent 2px,
            transparent 6px);
    transform: translate(-50%, -50%);
    border-radius: 4px;
}

.card-value {
    font-size: 12px;
    line-height: 1;
    font-weight: 600;
}

/* Bottom Controls */
.bottom-controls {
    position: fixed;
    bottom: 0;
    left: 0;
    right: 0;
    background: rgba(0, 0, 0, 0.9);
    backdrop-filter: blur(20px);
    border-top: 1px solid rgba(255, 255, 255, 0.1);
    padding: 20px;
    z-index: 1000;
}

/* Game Actions Section */
.game-actions-section {
    display: flex;
    justify-content: center;
    gap: 10px;
    margin-bottom: 20px;
    flex-wrap: wrap;
}

.action-btn {
    background: linear-gradient(145deg, #374151, #4b5563);
    border: 2px solid rgba(255, 255, 255, 0.1);
    border-radius: 12px;
    padding: 12px 16px;
    color: white;
    cursor: pointer;
    transition: all 0.3s ease;
    font-size: 14px;
    font-weight: 600;
    min-width: 80px;
    min-height: 50px;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    gap: 4px;
    position: relative;
    overflow: hidden;
}

.action-btn:hover:not(:disabled) {
    background: linear-gradient(145deg, #4b5563, #6b7280);
    border-color: rgba(255, 255, 255, 0.3);
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);
}

.action-btn:active:not(:disabled) {
    transform: translateY(0);
}

.action-btn:disabled {
    opacity: 0.5;
    cursor: not-allowed;
    background: linear-gradient(145deg, #1f2937, #374151);
}

.action-btn .btn-icon {
    font-size: 16px;
    line-height: 1;
}

.action-btn .btn-text {
    font-size: 12px;
    line-height: 1;
}

/* Specific Button Styles */
.hit-btn {
    background: linear-gradient(145deg, #059669, #10b981);
    border-color: rgba(16, 185, 129, 0.3);
}

.hit-btn:hover:not(:disabled) {
    background: linear-gradient(145deg, #10b981, #34d399);
}

.stand-btn {
    background: linear-gradient(145deg, #dc2626, #ef4444);
    border-color: rgba(239, 68, 68, 0.3);
}

.stand-btn:hover:not(:disabled) {
    background: linear-gradient(145deg, #ef4444, #f87171);
}

.double-btn {
    background: linear-gradient(145deg, #d97706, #f59e0b);
    border-color: rgba(245, 158, 11, 0.3);
}

.double-btn:hover:not(:disabled) {
    background: linear-gradient(145deg, #f59e0b, #fbbf24);
}

.split-btn {
    background: linear-gradient(145deg, #7c3aed, #8b5cf6);
    border-color: rgba(139, 92, 246, 0.3);
}

.split-btn:hover:not(:disabled) {
    background: linear-gradient(145deg, #8b5cf6, #a78bfa);
}

.surrender-btn {
    background: linear-gradient(145deg, #6b7280, #9ca3af);
    border-color: rgba(156, 163, 175, 0.3);
}

.surrender-btn:hover:not(:disabled) {
    background: linear-gradient(145deg, #9ca3af, #d1d5db);
}

.hint-btn {
    background: linear-gradient(145deg, #0891b2, #06b6d4);
    border-color: rgba(6, 182, 212, 0.3);
}

.hint-btn:hover:not(:disabled) {
    background: linear-gradient(145deg, #06b6d4, #22d3ee);
}

/* Chip Section */
.chip-section {
    display: flex;
    justify-content: space-between;
    align-items: center;
    gap: 20px;
}

.player-balance {
    background: rgba(255, 255, 255, 0.1);
    padding: 10px 20px;
    border-radius: 20px;
    font-size: 18px;
    font-weight: bold;
    color: #ffffff;
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.2);
}

.player-balance::before {
    content: '$';
    margin-right: 2px;
}

.chip-tray {
    display: flex;
    gap: 10px;
    flex: 1;
    justify-content: center;
    flex-wrap: wrap;
}

.chip {
    width: 50px;
    height: 50px;
    border-radius: 50%;
    border: 3px solid;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 12px;
    font-weight: bold;
    cursor: pointer;
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
}

.chip:hover {
    transform: scale(1.1) translateY(-2px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);
}

.chip:active {
    transform: scale(1.05);
}

/* Chip Colors */
.chip.chip-5 {
    background: linear-gradient(145deg, #ef4444, #dc2626);
    border-color: #b91c1c;
    color: white;
}

.chip.chip-10 {
    background: linear-gradient(145deg, #3b82f6, #2563eb);
    border-color: #1d4ed8;
    color: white;
}

.chip.chip-25 {
    background: linear-gradient(145deg, #10b981, #059669);
    border-color: #047857;
    color: white;
}

.chip.chip-50 {
    background: linear-gradient(145deg, #f59e0b, #d97706);
    border-color: #b45309;
    color: white;
}

.chip.chip-100 {
    background: linear-gradient(145deg, #1f2937, #111827);
    border-color: #374151;
    color: white;
}

.chip.chip-500 {
    background: linear-gradient(145deg, #7c3aed, #6d28d9);
    border-color: #5b21b6;
    color: white;
}

/* Betting Section */
.betting-section {
    display: flex;
    gap: 10px;
    align-items: center;
}

.clear-btn {
    background: linear-gradient(145deg, #6b7280, #4b5563);
    border-color: rgba(107, 114, 128, 0.3);
}

.clear-btn:hover:not(:disabled) {
    background: linear-gradient(145deg, #9ca3af, #6b7280);
}

.double-bet-btn {
    background: linear-gradient(145deg, #0891b2, #0e7490);
    border-color: rgba(8, 145, 178, 0.3);
}

.double-bet-btn:hover:not(:disabled) {
    background: linear-gradient(145deg, #06b6d4, #0891b2);
}

.deal-btn {
    background: linear-gradient(145deg, #059669, #047857);
    border-color: rgba(5, 150, 105, 0.3);
}

.deal-btn:hover:not(:disabled) {
    background: linear-gradient(145deg, #10b981, #059669);
}

/* Insurance Panel */
.insurance-panel {
    position: fixed;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    background: rgba(0, 0, 0, 0.95);
    border-radius: 20px;
    padding: 30px;
    z-index: 2000;
    backdrop-filter: blur(20px);
    border: 2px solid rgba(255, 255, 255, 0.1);
    max-width: 400px;
    width: 90%;
}

.insurance-content {
    text-align: center;
}

.insurance-title {
    font-size: 20px;
    font-weight: bold;
    color: #ffffff;
    margin-bottom: 15px;
}

.insurance-description {
    font-size: 14px;
    color: rgba(255, 255, 255, 0.8);
    margin-bottom: 15px;
    line-height: 1.5;
}

.insurance-amount {
    font-size: 16px;
    color: #f59e0b;
    font-weight: bold;
    margin-bottom: 20px;
}

.insurance-buttons {
    display: flex;
    gap: 15px;
    justify-content: center;
}

.insurance-btn {
    padding: 12px 24px;
    border: none;
    border-radius: 12px;
    font-size: 14px;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    min-width: 120px;
}

.buy-btn {
    background: linear-gradient(145deg, #059669, #047857);
    color: white;
}

.buy-btn:hover {
    background: linear-gradient(145deg, #10b981, #059669);
    transform: translateY(-2px);
}

.decline-btn {
    background: linear-gradient(145deg, #dc2626, #b91c1c);
    color: white;
}

.decline-btn:hover {
    background: linear-gradient(145deg, #ef4444, #dc2626);
    transform: translateY(-2px);
}

/* Loading Overlay */
.loading-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.8);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 3000;
    backdrop-filter: blur(10px);
}

.loading-content {
    text-align: center;
    color: white;
}

.loading-spinner {
    width: 50px;
    height: 50px;
    border: 4px solid rgba(255, 255, 255, 0.3);
    border-top: 4px solid #10b981;
    border-radius: 50%;
    animation: spin 1s linear infinite;
    margin: 0 auto 20px;
}

.loading-text {
    font-size: 18px;
    font-weight: 500;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* Modal Styles */
.modal {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.8);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 4000;
    backdrop-filter: blur(10px);
}

.modal-content {
    background: linear-gradient(145deg, #1f2937, #111827);
    border-radius: 20px;
    padding: 30px;
    max-width: 500px;
    width: 90%;
    max-height: 80vh;
    overflow-y: auto;
    border: 2px solid rgba(255, 255, 255, 0.1);
    position: relative;
}

/* Rules Modal */
.rules-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
    padding-bottom: 15px;
}

.rules-header h2 {
    color: #ffffff;
    font-size: 24px;
    margin: 0;
}

.rules-close, .settings-close {
    background: none;
    border: none;
    color: #ffffff;
    font-size: 24px;
    cursor: pointer;
    padding: 5px;
    border-radius: 50%;
    width: 35px;
    height: 35px;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: background 0.3s ease;
}

.rules-close:hover, .settings-close:hover {
    background: rgba(255, 255, 255, 0.1);
}

.rules-section {
    margin-bottom: 25px;
}

.rules-section h3 {
    color: #10b981;
    font-size: 18px;
    margin-bottom: 10px;
}

.rules-section p {
    color: rgba(255, 255, 255, 0.8);
    line-height: 1.6;
    margin-bottom: 10px;
}

.rules-section ul {
    color: rgba(255, 255, 255, 0.8);
    line-height: 1.6;
    padding-left: 20px;
}

.rules-section li {
    margin-bottom: 8px;
}

.rules-section strong {
    color: #ffffff;
}

/* Game Over Modal */
.game-over-content {
    text-align: center;
}

.game-over-header {
    margin-bottom: 25px;
}

.game-over-header h2 {
    color: #ef4444;
    font-size: 28px;
    margin-bottom: 10px;
}

.game-over-icon {
    font-size: 48px;
    margin-bottom: 10px;
}

.game-over-message {
    margin-bottom: 30px;
}

.main-message {
    color: #ffffff;
    font-size: 20px;
    font-weight: bold;
    margin-bottom: 15px;
}

.encouragement-message {
    color: rgba(255, 255, 255, 0.8);
    line-height: 1.6;
    margin-bottom: 15px;
}

.tip-message {
    color: #10b981;
    font-weight: 500;
    line-height: 1.6;
}

.game-over-actions {
    display: flex;
    justify-content: center;
}

.restart-btn {
    background: linear-gradient(145deg, #059669, #047857);
    border: none;
    border-radius: 15px;
    padding: 15px 30px;
    color: white;
    font-size: 16px;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    gap: 10px;
}

.restart-btn:hover {
    background: linear-gradient(145deg, #10b981, #059669);
    transform: translateY(-2px);
}

.restart-icon {
    font-size: 20px;
}

/* Share Incentive Modal */
.share-incentive-content {
    text-align: center;
}

.share-incentive-header {
    margin-bottom: 25px;
}

.share-incentive-header h2 {
    color: #f59e0b;
    font-size: 24px;
    margin-bottom: 10px;
}

.share-incentive-icon {
    font-size: 48px;
    margin-bottom: 10px;
}

.share-section {
    margin: 25px 0;
}

.share-text {
    margin-bottom: 20px;
}

.share-text p {
    color: rgba(255, 255, 255, 0.8);
    font-size: 16px;
}

.share-buttons {
    display: flex;
    justify-content: center;
    gap: 15px;
    flex-wrap: wrap;
}

.share-button {
    width: 50px;
    height: 50px;
    border: none;
    border-radius: 50%;
    font-size: 24px;
    cursor: pointer;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    justify-content: center;
}

.share-button:hover {
    transform: scale(1.1) translateY(-2px);
}

.share-button.facebook {
    background: #1877f2;
    color: white;
}

.share-button.twitter {
    background: #1da1f2;
    color: white;
}

.share-button.whatsapp {
    background: #25d366;
    color: white;
}

.share-button.telegram {
    background: #0088cc;
    color: white;
}

.share-incentive-actions {
    margin-top: 25px;
}

.skip-share-btn {
    background: linear-gradient(145deg, #6b7280, #4b5563);
    border: none;
    border-radius: 12px;
    padding: 12px 24px;
    color: white;
    font-size: 14px;
    cursor: pointer;
    transition: all 0.3s ease;
}

.skip-share-btn:hover {
    background: linear-gradient(145deg, #9ca3af, #6b7280);
    transform: translateY(-2px);
}

/* Settings Modal */
.settings-content {
    max-width: 400px;
}

.settings-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 25px;
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
    padding-bottom: 15px;
}

.settings-header h2 {
    color: #ffffff;
    font-size: 24px;
    margin: 0;
}

.settings-section {
    margin-bottom: 25px;
}

.settings-section h3 {
    color: #10b981;
    font-size: 18px;
    margin-bottom: 15px;
}

.setting-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 15px;
    padding: 10px 0;
}

.setting-item label {
    color: rgba(255, 255, 255, 0.8);
    font-size: 14px;
    font-weight: 500;
}

.setting-item select {
    background: rgba(255, 255, 255, 0.1);
    border: 1px solid rgba(255, 255, 255, 0.2);
    border-radius: 8px;
    padding: 8px 12px;
    color: white;
    font-size: 14px;
    min-width: 120px;
}

.setting-item input[type="checkbox"] {
    width: 20px;
    height: 20px;
    accent-color: #10b981;
}

.settings-actions {
    display: flex;
    gap: 15px;
    justify-content: center;
    border-top: 1px solid rgba(255, 255, 255, 0.1);
    padding-top: 20px;
}

.reset-btn {
    background: linear-gradient(145deg, #6b7280, #4b5563);
    border: none;
    border-radius: 12px;
    padding: 12px 20px;
    color: white;
    font-size: 14px;
    cursor: pointer;
    transition: all 0.3s ease;
}

.reset-btn:hover {
    background: linear-gradient(145deg, #9ca3af, #6b7280);
    transform: translateY(-2px);
}

.apply-btn {
    background: linear-gradient(145deg, #059669, #047857);
    border: none;
    border-radius: 12px;
    padding: 12px 20px;
    color: white;
    font-size: 14px;
    cursor: pointer;
    transition: all 0.3s ease;
}

.apply-btn:hover {
    background: linear-gradient(145deg, #10b981, #059669);
    transform: translateY(-2px);
}

/* Card Animations */
@keyframes cardDeal {
    0% {
        transform: translateY(-100px) rotateY(180deg) scale(0.8);
        opacity: 0;
    }
    50% {
        transform: translateY(-20px) rotateY(90deg) scale(0.9);
        opacity: 0.5;
    }
    100% {
        transform: translateY(0) rotateY(0deg) scale(1);
        opacity: 1;
    }
}

@keyframes cardFlip {
    0% {
        transform: rotateY(0deg);
    }
    50% {
        transform: rotateY(90deg);
    }
    100% {
        transform: rotateY(0deg);
    }
}

@keyframes chipFly {
    0% {
        transform: scale(1) translateY(0);
        opacity: 1;
    }
    50% {
        transform: scale(0.8) translateY(-30px);
        opacity: 0.8;
    }
    100% {
        transform: scale(1) translateY(0);
        opacity: 1;
    }
}

@keyframes pulse {
    0%, 100% {
        transform: scale(1);
        opacity: 1;
    }
    50% {
        transform: scale(1.05);
        opacity: 0.8;
    }
}

@keyframes fadeIn {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes slideUp {
    from {
        transform: translateY(100%);
        opacity: 0;
    }
    to {
        transform: translateY(0);
        opacity: 1;
    }
}

/* Animation Classes */
.card-deal {
    animation: cardDeal 0.6s ease-out;
}

.card-flip {
    animation: cardFlip 0.6s ease-in-out;
}

.chip-fly {
    animation: chipFly 0.8s ease-out;
}

.pulse {
    animation: pulse 1s ease-in-out infinite;
}

.fade-in {
    animation: fadeIn 0.5s ease-out;
}

.slide-up {
    animation: slideUp 0.5s ease-out;
}

/* Game State Classes */
.player-position.blackjack {
    background: radial-gradient(circle, rgba(16, 185, 129, 0.2) 0%, transparent 70%);
    border-radius: 20px;
}

.player-position.twenty-one {
    background: radial-gradient(circle, rgba(245, 158, 11, 0.2) 0%, transparent 70%);
    border-radius: 20px;
}

.player-position.bust {
    background: radial-gradient(circle, rgba(239, 68, 68, 0.2) 0%, transparent 70%);
    border-radius: 20px;
}

/* Flying Chip Animation */
.flying-chip {
    position: absolute;
    pointer-events: none;
    z-index: 1000;
    transition: all 0.8s cubic-bezier(0.25, 0.46, 0.45, 0.94);
}

/* Responsive Design */
@media (max-width: 768px) {
    .casino-table {
        padding: 80px 10px 180px;
    }

    .game-actions-section {
        gap: 8px;
    }

    .action-btn {
        min-width: 70px;
        min-height: 45px;
        padding: 10px 12px;
        font-size: 12px;
    }

    .action-btn .btn-icon {
        font-size: 14px;
    }

    .action-btn .btn-text {
        font-size: 10px;
    }

    .chip-section {
        flex-direction: column;
        gap: 15px;
    }

    .chip {
        width: 45px;
        height: 45px;
        font-size: 11px;
    }

    .player-balance {
        font-size: 16px;
        padding: 8px 16px;
    }

    .card {
        width: 50px;
        height: 70px;
        margin: 0 -12px;
    }

    .card-value {
        font-size: 10px;
    }

    .dealer-info {
        gap: 15px;
        padding: 8px 16px;
    }

    .dealer-title {
        font-size: 16px;
    }

    .dealer-score {
        font-size: 14px;
        padding: 4px 12px;
    }

    .game-status {
        font-size: 14px;
        padding: 12px 20px;
        max-width: 300px;
    }

    .modal-content {
        padding: 20px;
        width: 95%;
    }

    .deck-container {
        padding: 10px;
        gap: 10px;
    }

    .deck-card {
        width: 40px;
        height: 56px;
    }

    .deck-count {
        font-size: 16px;
    }

    .deck-label {
        font-size: 10px;
    }
}

@media (max-width: 480px) {
    .top-status {
        padding: 8px 15px;
        height: 50px;
    }

    .settings-button {
        padding: 6px 10px;
        font-size: 14px;
        min-width: 40px;
        min-height: 40px;
    }

    .bottom-controls {
        padding: 15px;
    }

    .game-actions-section {
        gap: 6px;
        margin-bottom: 15px;
    }

    .action-btn {
        min-width: 60px;
        min-height: 40px;
        padding: 8px 10px;
    }

    .chip-tray {
        gap: 8px;
    }

    .chip {
        width: 40px;
        height: 40px;
        font-size: 10px;
    }

    .betting-section {
        gap: 8px;
    }

    .betting-section .action-btn {
        min-width: 50px;
        padding: 8px;
    }
}

/* Utility Classes */
.hidden {
    display: none !important;
}

.invisible {
    opacity: 0 !important;
}

.disabled {
    pointer-events: none !important;
    opacity: 0.5 !important;
}

/* Focus and Accessibility */
button:focus,
select:focus,
input:focus {
    outline: 2px solid #10b981;
    outline-offset: 2px;
}

/* High contrast mode support */
@media (prefers-contrast: high) {
    .card {
        border: 2px solid #000;
    }

    .action-btn {
        border-width: 3px;
    }

    .game-status {
        border: 2px solid #fff;
    }
}

.loading-screen {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.9);
    display: flex;
    justify-content: center;
    align-items: center;
    z-index: 1000;
    transition: opacity 0.5s ease;
}

.loading-content {
    text-align: center;
    color: #ffd700;
}

.loading-content h2 {
    font-size: 2em;
    margin-bottom: 20px;
    text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.8);
}

.loading-bar {
    width: 300px;
    height: 10px;
    background: rgba(255, 255, 255, 0.2);
    border-radius: 5px;
    overflow: hidden;
    margin: 20px auto;
}

.loading-progress {
    height: 100%;
    background: linear-gradient(90deg, #ffd700, #ffed4e);
    width: 0%;
    transition: width 0.3s ease;
    border-radius: 5px;
}

#loading-text {
    font-size: 1.2em;
    margin-top: 15px;
    opacity: 0.8;
}

@media (max-width: 768px) {
    #game-canvas {
        border-radius: 0;
        max-width: 100vw;
        max-height: 100vh;
    }
}

@media (max-width: 480px) {
    .loading-content h2 {
        font-size: 1.5em;
    }

    .loading-bar {
        width: 250px;
    }

    #loading-text {
        font-size: 1em;
    }
}




