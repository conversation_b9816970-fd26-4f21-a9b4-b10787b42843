class Chip extends GameObject {
    constructor(value, x = 0, y = 0) {
        super(x, y);
        this.value = value;
        this.radius = 30;
        this.width = this.radius * 2;
        this.height = this.radius * 2;
        this.thickness = 8;
        
        this.targetX = x;
        this.targetY = y;
        this.targetZ = 0;
        this.z = 0;
        this.animating = false;
        this.animationSpeed = 0.12;
        
        this.hovered = false;
        this.selected = false;
        this.glowIntensity = 0;
        this.bounceHeight = 0;
        this.bouncing = false;
        
        this.velocity = { x: 0, y: 0, rotation: 0, bounce: 0 };
        this.acceleration = { x: 0, y: 0, rotation: 0 };
        this.friction = 0.95;
        
        this.colors = this.getChipColors(value);
        this.texture = this.createTexture();
    }

    getChipColors(value) {
        const colorMap = {
            5: { primary: '#ff6b6b', secondary: '#d63031', accent: '#ffffff', name: 'Red' },
            10: { primary: '#74b9ff', secondary: '#0984e3', accent: '#ffffff', name: '<PERSON>' },
            25: { primary: '#00b894', secondary: '#00a085', accent: '#ffffff', name: '<PERSON>' },
            50: { primary: '#fdcb6e', secondary: '#e17055', accent: '#2d3436', name: 'Orange' },
            100: { primary: '#6c5ce7', secondary: '#5f3dc4', accent: '#ffffff', name: 'Purple' }
        };
        
        return colorMap[value] || colorMap[5];
    }

    createTexture() {
        const canvas = document.createElement('canvas');
        const ctx = canvas.getContext('2d');
        const size = this.radius * 2 + 20;
        canvas.width = size;
        canvas.height = size;
        
        const centerX = size / 2;
        const centerY = size / 2;
        
        this.drawChipBase(ctx, centerX, centerY);
        this.drawChipEdge(ctx, centerX, centerY);
        this.drawChipPattern(ctx, centerX, centerY);
        this.drawChipValue(ctx, centerX, centerY);
        
        return canvas;
    }

    drawChipBase(ctx, centerX, centerY) {
        // Simplified gradient creation
        const gradient = ctx.createRadialGradient(centerX, centerY, 0, centerX, centerY, this.radius);
        gradient.addColorStop(0, this.colors.primary);
        gradient.addColorStop(0.7, this.colors.secondary);
        gradient.addColorStop(1, this.colors.primary);
        
        ctx.fillStyle = gradient;
        ctx.beginPath();
        ctx.arc(centerX, centerY, this.radius, 0, Math.PI * 2);
        ctx.fill();
        
        ctx.strokeStyle = this.colors.accent;
        ctx.lineWidth = 3;
        ctx.stroke();
    }

    drawChipEdge(ctx, centerX, centerY) {
        // Simplified gradient creation
        const edgeGradient = ctx.createLinearGradient(centerX, centerY - this.radius, centerX, centerY + this.radius);
        edgeGradient.addColorStop(0, 'rgba(255,255,255,0.3)');
        edgeGradient.addColorStop(0.5, 'rgba(255,255,255,0.1)');
        edgeGradient.addColorStop(1, 'rgba(0,0,0,0.2)');
        
        ctx.strokeStyle = edgeGradient;
        ctx.lineWidth = 2;
        ctx.beginPath();
        ctx.arc(centerX, centerY, this.radius - 2, 0, Math.PI * 2);
        ctx.stroke();
        
        for (let i = 0; i < 8; i++) {
            const angle = (i * Math.PI * 2) / 8;
            const x1 = centerX + Math.cos(angle) * (this.radius - 8);
            const y1 = centerY + Math.sin(angle) * (this.radius - 8);
            const x2 = centerX + Math.cos(angle) * (this.radius - 2);
            const y2 = centerY + Math.sin(angle) * (this.radius - 2);
            
            ctx.strokeStyle = this.colors.accent;
            ctx.lineWidth = 1;
            ctx.beginPath();
            ctx.moveTo(x1, y1);
            ctx.lineTo(x2, y2);
            ctx.stroke();
        }
    }

    drawChipPattern(ctx, centerX, centerY) {
        ctx.strokeStyle = this.colors.accent;
        ctx.lineWidth = 2;
        
        ctx.beginPath();
        ctx.arc(centerX, centerY, this.radius * 0.6, 0, Math.PI * 2);
        ctx.stroke();
        
        ctx.beginPath();
        ctx.arc(centerX, centerY, this.radius * 0.3, 0, Math.PI * 2);
        ctx.stroke();
        
        for (let i = 0; i < 4; i++) {
            const angle = (i * Math.PI * 2) / 4;
            const x = centerX + Math.cos(angle) * this.radius * 0.45;
            const y = centerY + Math.sin(angle) * this.radius * 0.45;
            
            ctx.fillStyle = this.colors.accent;
            ctx.beginPath();
            ctx.arc(x, y, 3, 0, Math.PI * 2);
            ctx.fill();
        }
        
        for (let i = 0; i < 12; i++) {
            const angle = (i * Math.PI * 2) / 12;
            const x1 = centerX + Math.cos(angle) * this.radius * 0.75;
            const y1 = centerY + Math.sin(angle) * this.radius * 0.75;
            const x2 = centerX + Math.cos(angle) * this.radius * 0.85;
            const y2 = centerY + Math.sin(angle) * this.radius * 0.85;
            
            ctx.strokeStyle = this.colors.accent;
            ctx.lineWidth = 1;
            ctx.beginPath();
            ctx.moveTo(x1, y1);
            ctx.lineTo(x2, y2);
            ctx.stroke();
        }
    }

    drawChipValue(ctx, centerX, centerY) {
        ctx.save();
        ctx.shadowColor = 'rgba(0,0,0,0.5)';
        ctx.shadowBlur = 2;
        ctx.shadowOffsetX = 1;
        ctx.shadowOffsetY = 1;

        ctx.font = `bold ${this.radius * 0.4}px Arial`;
        ctx.fillStyle = this.colors.accent;
        ctx.strokeStyle = this.colors.secondary;
        ctx.lineWidth = 2;
        ctx.textAlign = 'center';
        ctx.textBaseline = 'middle';

        ctx.strokeText(`$${this.value}`, centerX, centerY);
        ctx.fillText(`$${this.value}`, centerX, centerY);

        ctx.font = `${this.radius * 0.2}px Arial`;
        ctx.fillText(this.colors.name, centerX, centerY + this.radius * 0.6);

        ctx.restore();
    }

    update(deltaTime) {
        super.update(deltaTime);
        
        if (this.animating) {
            const dx = this.targetX - this.x;
            const dy = this.targetY - this.y;
            const dz = this.targetZ - this.z;
            const distance = Math.sqrt(dx * dx + dy * dy + dz * dz);
            
            if (distance > 1) {
                const speed = this.animationSpeed * (deltaTime / 16.67);
                this.x += dx * speed;
                this.y += dy * speed;
                this.z += dz * speed;
            } else {
                this.x = this.targetX;
                this.y = this.targetY;
                this.z = this.targetZ;
                this.animating = false;
            }
        }
        
        this.velocity.x += this.acceleration.x;
        this.velocity.y += this.acceleration.y;
        this.velocity.rotation += this.acceleration.rotation;
        
        this.x += this.velocity.x;
        this.y += this.velocity.y;
        this.rotation += this.velocity.rotation;
        
        this.velocity.x *= this.friction;
        this.velocity.y *= this.friction;
        this.velocity.rotation *= this.friction;
        
        this.acceleration.x = 0;
        this.acceleration.y = 0;
        this.acceleration.rotation = 0;
        
        if (this.bouncing) {
            this.bounceHeight += this.velocity.bounce;
            this.velocity.bounce += 0.5;
            
            if (this.bounceHeight <= 0) {
                this.bounceHeight = 0;
                this.velocity.bounce *= -0.7;
                
                if (Math.abs(this.velocity.bounce) < 1) {
                    this.bouncing = false;
                    this.velocity.bounce = 0;
                }
            }
        }
        
        if (this.hovered) {
            this.glowIntensity = Math.min(this.glowIntensity + 0.1, 1);
        } else {
            this.glowIntensity = Math.max(this.glowIntensity - 0.1, 0);
        }
    }

    draw(ctx, deltaTime) {
        this.drawShadow(ctx);

        const drawY = this.y - this.bounceHeight - this.z;

        ctx.save();
        ctx.translate(this.x, drawY);
        ctx.rotate(this.rotation);

        if (this.glowIntensity > 0) {
            this.drawGlow(ctx);
        }

        this.drawSimpleChip(ctx);

        if (this.hovered) {
            ctx.translate(0, -3);
        }

        ctx.restore();
    }

    drawSimpleChip(ctx) {
        // Simple chip drawing without complex gradients
        const gradient = ctx.createRadialGradient(0, 0, 0, 0, 0, this.radius);
        gradient.addColorStop(0, this.colors.primary);
        gradient.addColorStop(0.7, this.colors.secondary);
        gradient.addColorStop(1, this.colors.primary);

        ctx.fillStyle = gradient;
        ctx.beginPath();
        ctx.arc(0, 0, this.radius, 0, Math.PI * 2);
        ctx.fill();

        ctx.strokeStyle = this.colors.accent;
        ctx.lineWidth = 3;
        ctx.stroke();

        // Draw value text
        ctx.fillStyle = this.colors.accent;
        ctx.font = `bold ${this.radius * 0.4}px Arial`;
        ctx.textAlign = 'center';
        ctx.textBaseline = 'middle';
        ctx.fillText(`$${this.value}`, 0, 0);
    }

    drawShadow(ctx) {
        if (this.z > 0 || this.bounceHeight > 0 || this.hovered) {
            const shadowRadius = this.radius + (this.z + this.bounceHeight) * 0.3;
            const shadowX = this.x + (this.z + this.bounceHeight) * 0.2;
            const shadowY = this.y + (this.z + this.bounceHeight) * 0.2;
            const alpha = 0.3 * (this.hovered ? 1.5 : 1);
            
            ctx.save();
            ctx.globalAlpha = alpha;
            ctx.fillStyle = '#000000';
            ctx.filter = 'blur(6px)';
            ctx.beginPath();
            ctx.arc(shadowX, shadowY, shadowRadius, 0, Math.PI * 2);
            ctx.fill();
            ctx.restore();
        }
    }

    drawGlow(ctx) {
        ctx.save();
        ctx.globalAlpha = this.glowIntensity * 0.8;
        ctx.strokeStyle = '#ffd700';
        ctx.lineWidth = 6;
        ctx.filter = 'blur(3px)';
        ctx.beginPath();
        ctx.arc(0, 0, this.radius + 5, 0, Math.PI * 2);
        ctx.stroke();
        ctx.restore();
    }

    animateTo(x, y, z = 0, duration = 1000) {
        this.targetX = x;
        this.targetY = y;
        this.targetZ = z;
        this.animating = true;
        
        return new Promise(resolve => {
            setTimeout(() => {
                this.animating = false;
                resolve();
            }, duration);
        });
    }

    animateThrow(targetX, targetY, height = 50, duration = 800) {
        return new Promise(resolve => {
            const startX = this.x;
            const startY = this.y;
            const startTime = Date.now();
            
            this.velocity.rotation = (Math.random() - 0.5) * 0.6;
            
            const animate = () => {
                const elapsed = Date.now() - startTime;
                const progress = Math.min(elapsed / duration, 1);
                
                this.x = startX + (targetX - startX) * progress;
                
                const parabolicY = -4 * height * progress * (progress - 1);
                this.y = startY + (targetY - startY) * progress + parabolicY;
                
                if (progress < 1) {
                    requestAnimationFrame(animate);
                } else {
                    this.x = targetX;
                    this.y = targetY;
                    this.startBounce();
                    resolve();
                }
            };
            animate();
        });
    }

    startBounce() {
        this.bouncing = true;
        this.bounceHeight = 0;
        this.velocity.bounce = -8;
    }

    setHovered(hovered) {
        this.hovered = hovered;
    }

    setSelected(selected) {
        this.selected = selected;
    }

    applyForce(forceX, forceY, torque = 0) {
        this.acceleration.x += forceX;
        this.acceleration.y += forceY;
        this.acceleration.rotation += torque;
    }

    isPointInside(x, y) {
        const dx = x - this.x;
        const dy = y - this.y;
        return Math.sqrt(dx * dx + dy * dy) <= this.radius;
    }

    getBounds() {
        return {
            x: this.x - this.radius,
            y: this.y - this.radius,
            width: this.radius * 2,
            height: this.radius * 2
        };
    }
}
