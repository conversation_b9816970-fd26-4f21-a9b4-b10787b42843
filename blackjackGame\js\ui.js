class UIManager extends GameObject {
    constructor(renderEngine, gameLogic) {
        super(0, 0);
        this.renderEngine = renderEngine;
        this.gameLogic = gameLogic;
        this.width = renderEngine.getWidth();
        this.height = renderEngine.getHeight();
        
        this.buttons = [];
        this.panels = [];
        this.hoveredButton = null;
        this.showStats = false;
        this.showSettings = false;
        
        this.createUI();
    }

    createUI() {
        this.createGameButtons();
        this.createChipButtons();
        this.createInfoPanels();
        this.createModals();
    }

    createGameButtons() {
        const buttonWidth = 120;
        const buttonHeight = 40;
        const buttonSpacing = 10;
        const startX = this.width / 2 - (buttonWidth * 3 + buttonSpacing * 2) / 2;
        const y = this.height - 80;
        
        this.buttons.push(new Button(
            startX, y, buttonWidth, buttonHeight,
            'Deal Cards', 'deal', '#00b894', '#ffffff'
        ));
        
        this.buttons.push(new Button(
            startX + buttonWidth + buttonSpacing, y, buttonWidth, buttonHeight,
            'Hit', 'hit', '#74b9ff', '#ffffff'
        ));
        
        this.buttons.push(new Button(
            startX + (buttonWidth + buttonSpacing) * 2, y, buttonWidth, buttonHeight,
            'Stand', 'stand', '#e17055', '#ffffff'
        ));
        
        this.buttons.push(new Button(
            startX + (buttonWidth + buttonSpacing) * 3, y, buttonWidth, buttonHeight,
            'Double', 'double', '#fdcb6e', '#2d3436'
        ));
        
        this.buttons.push(new Button(
            this.width - 140, 20, 120, 35,
            'New Game', 'newGame', '#636e72', '#ffffff'
        ));
        
        this.buttons.push(new Button(
            20, 20, 80, 35,
            'Stats', 'stats', '#6c5ce7', '#ffffff'
        ));
        
        this.buttons.push(new Button(
            110, 20, 80, 35,
            'Settings', 'settings', '#a29bfe', '#ffffff'
        ));
    }

    createChipButtons() {
        const chipValues = [5, 10, 25, 50, 100];
        const chipSpacing = 80;
        const startX = this.width / 2 - (chipSpacing * (chipValues.length - 1)) / 2;
        const y = this.height - 160;
        
        chipValues.forEach((value, index) => {
            this.buttons.push(new ChipButton(
                startX + index * chipSpacing, y, value
            ));
        });
        
        this.buttons.push(new Button(
            this.width / 2 - 60, y + 60, 120, 30,
            'Clear Bet', 'clearBet', '#d63031', '#ffffff'
        ));
    }

    createInfoPanels() {
        this.panels.push(new InfoPanel(
            20, this.height - 200, 200, 120,
            'Player Info'
        ));
        
        this.panels.push(new InfoPanel(
            this.width - 220, this.height - 200, 200, 120,
            'Game Info'
        ));
        
        this.panels.push(new InfoPanel(
            20, 70, 200, 100,
            'Dealer Info'
        ));
    }

    createModals() {
        // Stats and settings modals will be created when needed
    }

    update(deltaTime) {
        super.update(deltaTime);
        
        this.buttons.forEach(button => button.update(deltaTime));
        this.panels.forEach(panel => panel.update(deltaTime));
    }

    draw(ctx, deltaTime) {
        this.drawBackground(ctx);
        this.drawGameInfo(ctx);
        this.drawHandValues(ctx);
        
        this.panels.forEach(panel => panel.draw(ctx, deltaTime));
        this.buttons.forEach(button => button.draw(ctx, deltaTime));
        
        // Modals temporarily disabled to avoid Utils errors
        // if (this.showStats) {
        //     this.drawStatsModal(ctx);
        // }
        //
        // if (this.showSettings) {
        //     this.drawSettingsModal(ctx);
        // }
        
        this.drawGameMessage(ctx);
    }

    drawBackground(ctx) {
        // Background is drawn by render engine
    }

    drawGameInfo(ctx) {
        const balance = '$' + this.gameLogic.balance;
        const bet = '$' + this.gameLogic.currentBet;
        const winRate = this.gameLogic.getWinRate() + '%';

        ctx.font = 'bold 16px Arial';
        ctx.fillStyle = '#ffd700';
        ctx.textAlign = 'left';

        ctx.fillText(`Balance: ${balance}`, this.width - 220, this.height - 180);
        ctx.fillText(`Bet: ${bet}`, this.width - 220, this.height - 160);
        ctx.fillText(`Win Rate: ${winRate}`, this.width - 220, this.height - 140);
    }

    drawHandValues(ctx) {
        const playerValue = this.gameLogic.getHandValue(this.gameLogic.playerHand);
        const dealerValue = this.gameLogic.gameState === 'betting' || this.gameLogic.gameState === 'dealing' || this.gameLogic.gameState === 'playing' ?
            (this.gameLogic.dealerHand.length > 0 ? this.gameLogic.getCardValue(this.gameLogic.dealerHand[0]) : 0) :
            this.gameLogic.getHandValue(this.gameLogic.dealerHand);
        
        ctx.font = 'bold 18px Arial';
        ctx.fillStyle = '#ffffff';
        ctx.strokeStyle = '#000000';
        ctx.lineWidth = 2;
        ctx.textAlign = 'left';

        ctx.strokeText(`Player: ${playerValue}`, 40, this.height - 160);
        ctx.fillText(`Player: ${playerValue}`, 40, this.height - 160);

        ctx.strokeText(`Dealer: ${dealerValue}`, 40, 140);
        ctx.fillText(`Dealer: ${dealerValue}`, 40, 140);
    }

    drawGameMessage(ctx) {
        const message = this.gameLogic.getGameStateMessage();

        ctx.save();
        ctx.shadowColor = 'rgba(0,0,0,0.8)';
        ctx.shadowBlur = 5;
        ctx.shadowOffsetX = 2;
        ctx.shadowOffsetY = 2;

        ctx.font = 'bold 24px Arial';
        ctx.fillStyle = '#ffd700';
        ctx.strokeStyle = '#000000';
        ctx.lineWidth = 3;
        ctx.textAlign = 'center';

        ctx.strokeText(message, this.width / 2, 50);
        ctx.fillText(message, this.width / 2, 50);

        ctx.restore();
    }

    drawStatsModal(ctx) {
        const modalWidth = 400;
        const modalHeight = 300;
        const modalX = (this.width - modalWidth) / 2;
        const modalY = (this.height - modalHeight) / 2;
        
        ctx.save();
        ctx.globalAlpha = 0.8;
        ctx.fillStyle = '#000000';
        ctx.fillRect(0, 0, this.width, this.height);
        ctx.restore();
        
        ctx.fillStyle = '#2d3436';
        Utils.drawRoundedRect(ctx, modalX, modalY, modalWidth, modalHeight, 15);
        ctx.fill();
        
        ctx.strokeStyle = '#ffd700';
        ctx.lineWidth = 3;
        Utils.drawRoundedRect(ctx, modalX, modalY, modalWidth, modalHeight, 15);
        ctx.stroke();
        
        Utils.drawText(ctx, 'Game Statistics', modalX + modalWidth / 2, modalY + 30, {
            font: 'bold 24px Arial',
            fillStyle: '#ffd700',
            textAlign: 'center'
        });
        
        const stats = this.gameLogic.stats;
        const statsText = [
            `Games Played: ${stats.gamesPlayed}`,
            `Games Won: ${stats.gamesWon}`,
            `Win Rate: ${this.gameLogic.getWinRate()}%`,
            `Blackjacks: ${stats.blackjacks}`,
            `Biggest Win: ${Utils.formatCurrency(stats.biggestWin)}`,
            `Current Streak: ${stats.currentStreak}`,
            `Longest Streak: ${stats.longestStreak}`
        ];
        
        statsText.forEach((text, index) => {
            Utils.drawText(ctx, text, modalX + 30, modalY + 80 + index * 25, {
                font: '16px Arial',
                fillStyle: '#ffffff',
                textAlign: 'left'
            });
        });
        
        Utils.drawText(ctx, 'Click anywhere to close', modalX + modalWidth / 2, modalY + modalHeight - 20, {
            font: '14px Arial',
            fillStyle: '#a0a0a0',
            textAlign: 'center'
        });
    }

    drawSettingsModal(ctx) {
        const modalWidth = 350;
        const modalHeight = 250;
        const modalX = (this.width - modalWidth) / 2;
        const modalY = (this.height - modalHeight) / 2;
        
        ctx.save();
        ctx.globalAlpha = 0.8;
        ctx.fillStyle = '#000000';
        ctx.fillRect(0, 0, this.width, this.height);
        ctx.restore();
        
        ctx.fillStyle = '#2d3436';
        Utils.drawRoundedRect(ctx, modalX, modalY, modalWidth, modalHeight, 15);
        ctx.fill();
        
        ctx.strokeStyle = '#ffd700';
        ctx.lineWidth = 3;
        Utils.drawRoundedRect(ctx, modalX, modalY, modalWidth, modalHeight, 15);
        ctx.stroke();
        
        Utils.drawText(ctx, 'Game Settings', modalX + modalWidth / 2, modalY + 30, {
            font: 'bold 24px Arial',
            fillStyle: '#ffd700',
            textAlign: 'center'
        });
        
        Utils.drawText(ctx, 'Sound: ON/OFF', modalX + 30, modalY + 80, {
            font: '16px Arial',
            fillStyle: '#ffffff',
            textAlign: 'left'
        });
        
        Utils.drawText(ctx, 'Animation Speed: Normal', modalX + 30, modalY + 110, {
            font: '16px Arial',
            fillStyle: '#ffffff',
            textAlign: 'left'
        });
        
        Utils.drawText(ctx, 'Auto-Play: OFF', modalX + 30, modalY + 140, {
            font: '16px Arial',
            fillStyle: '#ffffff',
            textAlign: 'left'
        });
        
        Utils.drawText(ctx, 'Click anywhere to close', modalX + modalWidth / 2, modalY + modalHeight - 20, {
            font: '14px Arial',
            fillStyle: '#a0a0a0',
            textAlign: 'center'
        });
    }

    handleClick(x, y) {
        if (this.showStats || this.showSettings) {
            this.showStats = false;
            this.showSettings = false;
            return;
        }
        
        for (let button of this.buttons) {
            if (button.isPointInside(x, y) && button.enabled) {
                this.handleButtonClick(button);
                return;
            }
        }
    }

    handleButtonClick(button) {
        switch (button.action) {
            case 'deal':
                if (this.gameLogic.startGame()) {
                    // Game will handle the dealing animation
                }
                break;
            case 'hit':
                this.gameLogic.hit();
                break;
            case 'stand':
                this.gameLogic.stand();
                break;
            case 'double':
                this.gameLogic.doubleDown();
                break;
            case 'newGame':
                this.gameLogic.newGame();
                break;
            case 'clearBet':
                this.gameLogic.clearBet();
                break;
            case 'stats':
                this.showStats = true;
                break;
            case 'settings':
                this.showSettings = true;
                break;
            case 'chip':
                this.gameLogic.placeBet(button.value);
                break;
        }
    }

    handleMouseMove(x, y) {
        this.hoveredButton = null;
        
        for (let button of this.buttons) {
            const wasHovered = button.hovered;
            button.hovered = button.isPointInside(x, y) && button.enabled;
            
            if (button.hovered && !wasHovered) {
                this.hoveredButton = button;
            }
        }
    }

    updateButtonStates() {
        const gameState = this.gameLogic.gameState;
        const canDoubleDown = this.gameLogic.canDoubleDown;
        const canSplit = this.gameLogic.canSplit;
        const hasBet = this.gameLogic.currentBet > 0;
        
        this.buttons.forEach(button => {
            switch (button.action) {
                case 'deal':
                    button.enabled = gameState === 'betting' && hasBet;
                    break;
                case 'hit':
                case 'stand':
                    button.enabled = gameState === 'playing';
                    break;
                case 'double':
                    button.enabled = gameState === 'playing' && canDoubleDown;
                    break;
                case 'split':
                    button.enabled = gameState === 'playing' && canSplit;
                    break;
                case 'clearBet':
                    button.enabled = gameState === 'betting' && hasBet;
                    break;
                case 'chip':
                    button.enabled = gameState === 'betting';
                    break;
            }
        });
    }
}

class Button extends GameObject {
    constructor(x, y, width, height, text, action, color = '#74b9ff', textColor = '#ffffff') {
        super(x, y);
        this.width = width;
        this.height = height;
        this.text = text;
        this.action = action;
        this.color = color;
        this.textColor = textColor;
        this.hovered = false;
        this.enabled = true;
        this.pressed = false;
    }

    draw(ctx, deltaTime) {
        const alpha = this.enabled ? 1 : 0.5;
        const scale = this.pressed ? 0.95 : (this.hovered ? 1.05 : 1);
        
        ctx.save();
        ctx.globalAlpha = alpha;
        ctx.translate(this.x + this.width / 2, this.y + this.height / 2);
        ctx.scale(scale, scale);
        ctx.translate(-this.width / 2, -this.height / 2);
        
        if (this.hovered && this.enabled) {
            ctx.save();
            ctx.globalAlpha = 0.8;
            ctx.strokeStyle = '#ffd700';
            ctx.lineWidth = 8;
            ctx.filter = 'blur(4px)';
            ctx.strokeRect(-5, -5, this.width + 10, this.height + 10);
            ctx.restore();
        }

        const gradient = ctx.createLinearGradient(0, 0, 0, this.height);
        gradient.addColorStop(0, this.color);
        gradient.addColorStop(1, this.color);
        
        ctx.fillStyle = gradient;
        ctx.fillRect(0, 0, this.width, this.height);

        ctx.strokeStyle = this.hovered ? '#ffd700' : '#ffffff';
        ctx.lineWidth = 2;
        ctx.strokeRect(1, 1, this.width - 2, this.height - 2);

        ctx.save();
        ctx.shadowColor = 'rgba(0,0,0,0.5)';
        ctx.shadowBlur = 2;
        ctx.shadowOffsetX = 1;
        ctx.shadowOffsetY = 1;

        ctx.font = 'bold 14px Arial';
        ctx.fillStyle = this.textColor;
        ctx.textAlign = 'center';
        ctx.textBaseline = 'middle';
        ctx.fillText(this.text, this.width / 2, this.height / 2);

        ctx.restore();
        
        ctx.restore();
    }



    isPointInside(x, y) {
        return x >= this.x && x <= this.x + this.width &&
               y >= this.y && y <= this.y + this.height;
    }
}

class ChipButton extends Button {
    constructor(x, y, value) {
        super(x, y, 60, 60, `$${value}`, 'chip');
        this.value = value;
        this.radius = 30;
        this.colors = this.getChipColors(value);
    }

    getChipColors(value) {
        const colorMap = {
            5: { primary: '#ff6b6b', secondary: '#d63031' },
            10: { primary: '#74b9ff', secondary: '#0984e3' },
            25: { primary: '#00b894', secondary: '#00a085' },
            50: { primary: '#fdcb6e', secondary: '#e17055' },
            100: { primary: '#6c5ce7', secondary: '#5f3dc4' }
        };
        return colorMap[value] || colorMap[5];
    }

    draw(ctx, deltaTime) {
        const alpha = this.enabled ? 1 : 0.5;
        const scale = this.pressed ? 0.9 : (this.hovered ? 1.1 : 1);
        
        ctx.save();
        ctx.globalAlpha = alpha;
        ctx.translate(this.x + this.radius, this.y + this.radius);
        ctx.scale(scale, scale);
        
        if (this.hovered && this.enabled) {
            ctx.save();
            ctx.globalAlpha = 0.8;
            ctx.strokeStyle = '#ffd700';
            ctx.lineWidth = 6;
            ctx.filter = 'blur(3px)';
            ctx.beginPath();
            ctx.arc(0, 0, this.radius + 5, 0, Math.PI * 2);
            ctx.stroke();
            ctx.restore();
        }
        
        const gradient = ctx.createRadialGradient(0, 0, 0, 0, 0, this.radius);
        gradient.addColorStop(0, this.colors.primary);
        gradient.addColorStop(0.7, this.colors.secondary);
        gradient.addColorStop(1, this.colors.primary);
        
        ctx.fillStyle = gradient;
        ctx.beginPath();
        ctx.arc(0, 0, this.radius, 0, Math.PI * 2);
        ctx.fill();
        
        ctx.strokeStyle = '#ffffff';
        ctx.lineWidth = 3;
        ctx.stroke();
        
        Utils.drawText(ctx, this.text, 0, 0, {
            font: 'bold 12px Arial',
            fillStyle: '#ffffff',
            strokeStyle: this.colors.secondary,
            lineWidth: 2,
            textAlign: 'center',
            textBaseline: 'middle'
        });
        
        ctx.restore();
    }

    isPointInside(x, y) {
        return Utils.isPointInCircle(x, y, {
            x: this.x + this.radius,
            y: this.y + this.radius,
            radius: this.radius
        });
    }
}

class InfoPanel extends GameObject {
    constructor(x, y, width, height, title) {
        super(x, y);
        this.width = width;
        this.height = height;
        this.title = title;
    }

    draw(ctx, deltaTime) {
        ctx.save();
        ctx.globalAlpha = 0.8;
        
        ctx.fillStyle = 'rgba(0, 0, 0, 0.6)';
        Utils.drawRoundedRect(ctx, this.x, this.y, this.width, this.height, 10);
        ctx.fill();
        
        ctx.strokeStyle = '#ffd700';
        ctx.lineWidth = 2;
        Utils.drawRoundedRect(ctx, this.x, this.y, this.width, this.height, 10);
        ctx.stroke();
        
        Utils.drawText(ctx, this.title, this.x + this.width / 2, this.y + 20, {
            font: 'bold 16px Arial',
            fillStyle: '#ffd700',
            textAlign: 'center'
        });
        
        ctx.restore();
    }
}
