class GameLoader {
    constructor() {
        this.loadingScreen = document.getElementById('loading-screen');
        this.loadingProgress = document.getElementById('loading-progress');
        this.loadingText = document.getElementById('loading-text');
        this.canvas = document.getElementById('game-canvas');

        this.loadingSteps = [
            { text: 'Initializing game engine...', duration: 500 },
            { text: 'Loading card textures...', duration: 800 },
            { text: 'Creating chip graphics...', duration: 600 },
            { text: 'Setting up audio system...', duration: 400 },
            { text: 'Preparing game table...', duration: 700 },
            { text: 'Shuffling deck...', duration: 500 },
            { text: 'Ready to play!', duration: 300 }
        ];

        this.currentStep = 0;
        this.game = null;

        this.startLoading();
    }

    async startLoading() {
        this.setupCanvas();

        for (let i = 0; i < this.loadingSteps.length; i++) {
            const step = this.loadingSteps[i];
            this.updateLoadingText(step.text);
            this.updateLoadingProgress((i + 1) / this.loadingSteps.length * 100);

            await Utils.sleep(step.duration);
        }

        await this.initializeGame();
        this.hideLoadingScreen();
    }

    setupCanvas() {
        const container = this.canvas.parentElement;
        const rect = container.getBoundingClientRect();

        this.canvas.width = rect.width * (window.devicePixelRatio || 1);
        this.canvas.height = rect.height * (window.devicePixelRatio || 1);
        this.canvas.style.width = rect.width + 'px';
        this.canvas.style.height = rect.height + 'px';
    }

    updateLoadingText(text) {
        this.loadingText.textContent = text;
    }

    updateLoadingProgress(percentage) {
        this.loadingProgress.style.width = percentage + '%';
    }

    async initializeGame() {
        try {
            console.log('Starting game initialization...');

            // Test basic functionality first
            console.log('Testing Utils...');
            const testGradient = Utils.createLinearGradient(
                this.canvas.getContext('2d'),
                0, 0, 100, 100,
                ['#ff0000', '#00ff00']
            );
            console.log('Utils test passed');

            console.log('Creating BlackjackGame...');
            this.game = new BlackjackGame(this.canvas);

            this.setupKeyboardControls();

            if (Utils.isMobile()) {
                this.setupTouchControls();
            }

            window.addEventListener('resize', Utils.debounce(() => {
                this.game.handleResize();
            }, 250));

            console.log('Professional Blackjack Game initialized successfully!');

        } catch (error) {
            console.error('Failed to initialize game:', error);
            console.error('Error stack:', error.stack);
            this.showError('Failed to load game. Please refresh the page.');
        }
    }

    setupKeyboardControls() {
        document.addEventListener('keydown', (e) => {
            if (!this.game) return;

            const gameState = this.game.getGameState();

            switch (e.key.toLowerCase()) {
                case 'h':
                    if (gameState === 'playing') {
                        this.game.hit();
                        e.preventDefault();
                    }
                    break;
                case 's':
                    if (gameState === 'playing') {
                        this.game.stand();
                        e.preventDefault();
                    }
                    break;
                case 'd':
                    if (gameState === 'playing' && this.game.gameLogic.canDoubleDown) {
                        this.game.doubleDown();
                        e.preventDefault();
                    }
                    break;
                case ' ':
                    if (gameState === 'betting' && this.game.getCurrentBet() > 0) {
                        this.game.gameLogic.startGame();
                        e.preventDefault();
                    }
                    break;
                case 'n':
                    this.game.newGame();
                    e.preventDefault();
                    break;
                case '1':
                case '2':
                case '3':
                case '4':
                case '5':
                    if (gameState === 'betting') {
                        const chipValues = [5, 10, 25, 50, 100];
                        const chipValue = chipValues[parseInt(e.key) - 1];
                        if (chipValue) {
                            this.game.placeBet(chipValue);
                        }
                        e.preventDefault();
                    }
                    break;
                case 'c':
                    if (gameState === 'betting') {
                        this.game.clearBet();
                        e.preventDefault();
                    }
                    break;
            }
        });
    }

    setupTouchControls() {
        let touchStartX = 0;
        let touchStartY = 0;

        this.canvas.addEventListener('touchstart', (e) => {
            e.preventDefault();
            const touch = e.touches[0];
            touchStartX = touch.clientX;
            touchStartY = touch.clientY;
        });

        this.canvas.addEventListener('touchend', (e) => {
            e.preventDefault();
            const touch = e.changedTouches[0];
            const touchEndX = touch.clientX;
            const touchEndY = touch.clientY;

            const deltaX = touchEndX - touchStartX;
            const deltaY = touchEndY - touchStartY;
            const distance = Math.sqrt(deltaX * deltaX + deltaY * deltaY);

            if (distance < 10) {
                const pos = Utils.getCanvasMousePos(this.canvas, touch);
                this.game.handleClick(pos.x, pos.y);
            }
        });

        this.canvas.addEventListener('touchmove', (e) => {
            e.preventDefault();
        });
    }

    hideLoadingScreen() {
        this.loadingScreen.style.opacity = '0';
        setTimeout(() => {
            this.loadingScreen.style.display = 'none';
        }, 500);
    }

    showError(message) {
        this.loadingText.textContent = message;
        this.loadingText.style.color = '#ff6b6b';
        this.loadingProgress.style.background = '#ff6b6b';
    }
}

document.addEventListener('DOMContentLoaded', () => {
    new GameLoader();
});

document.addEventListener('contextmenu', (e) => {
    if (e.target.tagName === 'CANVAS') {
        e.preventDefault();
    }
});

document.addEventListener('visibilitychange', () => {
    if (document.hidden) {
        console.log('Game paused (tab hidden)');
    } else {
        console.log('Game resumed (tab visible)');
    }
});