class SoundManager {
    constructor() {
        this.audioContext = null;
        this.enabled = true;
        this.volume = 0.3;
        this.sounds = new Map();
        
        this.initAudioContext();
        this.createSounds();
    }

    initAudioContext() {
        try {
            this.audioContext = new (window.AudioContext || window.webkitAudioContext)();
        } catch (error) {
            console.warn('Web Audio API not supported:', error);
            this.enabled = false;
        }
    }

    createSounds() {
        this.sounds.set('cardDeal', {
            frequency: 600,
            duration: 0.15,
            type: 'triangle',
            volume: 0.2
        });

        this.sounds.set('cardFlip', {
            frequency: 800,
            duration: 0.1,
            type: 'sine',
            volume: 0.15
        });

        this.sounds.set('chipPlace', {
            frequency: 1000,
            duration: 0.1,
            type: 'square',
            volume: 0.2
        });

        this.sounds.set('chipStack', {
            frequency: 1200,
            duration: 0.08,
            type: 'sawtooth',
            volume: 0.15
        });

        this.sounds.set('button', {
            frequency: 440,
            duration: 0.05,
            type: 'sine',
            volume: 0.1
        });

        this.sounds.set('win', () => this.playMelody([523, 659, 784, 1047], 0.3));
        this.sounds.set('lose', () => this.playMelody([392, 330, 262], 0.4));
        this.sounds.set('blackjack', () => this.playMelody([523, 659, 784, 1047, 1319], 0.5));
        this.sounds.set('push', () => this.playMelody([440, 440, 440], 0.2));

        this.sounds.set('shuffle', () => {
            for (let i = 0; i < 5; i++) {
                setTimeout(() => {
                    this.playBeep(Utils.randomBetween(200, 400), 0.05, 'noise', 0.1);
                }, i * 50);
            }
        });

        this.sounds.set('deal', () => {
            this.playBeep(600, 0.15, 'triangle', 0.2);
            setTimeout(() => {
                this.playBeep(500, 0.1, 'triangle', 0.15);
            }, 100);
        });
    }

    play(soundName) {
        if (!this.enabled || !this.audioContext) return;

        const sound = this.sounds.get(soundName);
        if (!sound) return;

        if (typeof sound === 'function') {
            sound();
        } else {
            this.playBeep(sound.frequency, sound.duration, sound.type, sound.volume * this.volume);
        }
    }

    playBeep(frequency, duration, type = 'sine', volume = 0.1) {
        if (!this.enabled || !this.audioContext) return;

        try {
            const oscillator = this.audioContext.createOscillator();
            const gainNode = this.audioContext.createGain();

            oscillator.connect(gainNode);
            gainNode.connect(this.audioContext.destination);

            oscillator.frequency.value = frequency;
            oscillator.type = type;

            gainNode.gain.setValueAtTime(0, this.audioContext.currentTime);
            gainNode.gain.linearRampToValueAtTime(volume, this.audioContext.currentTime + 0.01);
            gainNode.gain.exponentialRampToValueAtTime(0.01, this.audioContext.currentTime + duration);

            oscillator.start(this.audioContext.currentTime);
            oscillator.stop(this.audioContext.currentTime + duration);
        } catch (error) {
            console.warn('Sound playback failed:', error);
        }
    }

    playMelody(frequencies, noteDuration) {
        if (!this.enabled || !this.audioContext) return;

        frequencies.forEach((freq, index) => {
            setTimeout(() => {
                this.playBeep(freq, noteDuration, 'sine', this.volume * 0.3);
            }, index * noteDuration * 1000);
        });
    }

    playChord(frequencies, duration, volume = 0.1) {
        if (!this.enabled || !this.audioContext) return;

        frequencies.forEach(freq => {
            this.playBeep(freq, duration, 'sine', volume * this.volume);
        });
    }

    createNoise(duration, volume = 0.1) {
        if (!this.enabled || !this.audioContext) return;

        try {
            const bufferSize = this.audioContext.sampleRate * duration;
            const buffer = this.audioContext.createBuffer(1, bufferSize, this.audioContext.sampleRate);
            const data = buffer.getChannelData(0);

            for (let i = 0; i < bufferSize; i++) {
                data[i] = Math.random() * 2 - 1;
            }

            const source = this.audioContext.createBufferSource();
            const gainNode = this.audioContext.createGain();

            source.buffer = buffer;
            source.connect(gainNode);
            gainNode.connect(this.audioContext.destination);

            gainNode.gain.setValueAtTime(volume * this.volume, this.audioContext.currentTime);
            gainNode.gain.exponentialRampToValueAtTime(0.01, this.audioContext.currentTime + duration);

            source.start(this.audioContext.currentTime);
        } catch (error) {
            console.warn('Noise generation failed:', error);
        }
    }

    setVolume(volume) {
        this.volume = Utils.clamp(volume, 0, 1);
    }

    getVolume() {
        return this.volume;
    }

    setEnabled(enabled) {
        this.enabled = enabled;
    }

    isEnabled() {
        return this.enabled;
    }

    toggle() {
        this.enabled = !this.enabled;
        return this.enabled;
    }

    resumeContext() {
        if (this.audioContext && this.audioContext.state === 'suspended') {
            this.audioContext.resume();
        }
    }
}

class AudioVisualizer {
    constructor(audioContext, canvas) {
        this.audioContext = audioContext;
        this.canvas = canvas;
        this.ctx = canvas.getContext('2d');
        this.analyser = null;
        this.dataArray = null;
        this.isActive = false;
        
        this.setupAnalyser();
    }

    setupAnalyser() {
        if (!this.audioContext) return;

        this.analyser = this.audioContext.createAnalyser();
        this.analyser.fftSize = 256;
        this.dataArray = new Uint8Array(this.analyser.frequencyBinCount);
    }

    start() {
        this.isActive = true;
        this.draw();
    }

    stop() {
        this.isActive = false;
    }

    draw() {
        if (!this.isActive || !this.analyser) return;

        this.analyser.getByteFrequencyData(this.dataArray);

        this.ctx.save();
        this.ctx.globalAlpha = 0.1;
        this.ctx.fillStyle = '#ffd700';

        const barWidth = this.canvas.width / this.dataArray.length;
        let x = 0;

        for (let i = 0; i < this.dataArray.length; i++) {
            const barHeight = (this.dataArray[i] / 255) * this.canvas.height * 0.5;
            
            this.ctx.fillRect(x, this.canvas.height - barHeight, barWidth, barHeight);
            x += barWidth;
        }

        this.ctx.restore();

        if (this.isActive) {
            requestAnimationFrame(() => this.draw());
        }
    }

    connectSource(source) {
        if (this.analyser) {
            source.connect(this.analyser);
        }
    }
}
