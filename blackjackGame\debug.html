<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Blackjack Debug</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background: #f0f0f0;
        }
        .debug-container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .debug-section {
            margin-bottom: 20px;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 5px;
        }
        .status {
            padding: 10px;
            border-radius: 5px;
            margin: 10px 0;
        }
        .success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .error {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        .info {
            background: #d1ecf1;
            color: #0c5460;
            border: 1px solid #bee5eb;
        }
        button {
            background: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background: #0056b3;
        }
        iframe {
            width: 100%;
            height: 800px;
            border: 1px solid #ddd;
            border-radius: 5px;
        }
        .console-output {
            background: #1e1e1e;
            color: #fff;
            padding: 15px;
            border-radius: 5px;
            font-family: 'Courier New', monospace;
            font-size: 12px;
            max-height: 300px;
            overflow-y: auto;
        }
    </style>
</head>
<body>
    <div class="debug-container">
        <h1>🃏 Blackjack Game Debug Console</h1>
        
        <div class="debug-section">
            <h3>📊 System Status</h3>
            <div id="system-status">
                <div class="status info">
                    <strong>Status:</strong> Initializing...
                </div>
            </div>
            <button onclick="checkSystem()">Check System</button>
            <button onclick="clearConsole()">Clear Console</button>
        </div>

        <div class="debug-section">
            <h3>🎮 Game Preview</h3>
            <iframe id="game-frame" src="index.html"></iframe>
        </div>

        <div class="debug-section">
            <h3>📝 Console Output</h3>
            <div id="console-output" class="console-output">
                Console output will appear here...
            </div>
        </div>
    </div>

    <script>
        let consoleOutput = document.getElementById('console-output');
        
        // Capture console messages
        const originalLog = console.log;
        const originalError = console.error;
        const originalWarn = console.warn;
        
        function addToConsole(message, type = 'log') {
            const timestamp = new Date().toLocaleTimeString();
            const color = type === 'error' ? '#ff6b6b' : type === 'warn' ? '#feca57' : '#48dbfb';
            consoleOutput.innerHTML += `<div style="color: ${color}">[${timestamp}] ${type.toUpperCase()}: ${message}</div>`;
            consoleOutput.scrollTop = consoleOutput.scrollHeight;
        }
        
        console.log = function(...args) {
            originalLog.apply(console, args);
            addToConsole(args.join(' '), 'log');
        };
        
        console.error = function(...args) {
            originalError.apply(console, args);
            addToConsole(args.join(' '), 'error');
        };
        
        console.warn = function(...args) {
            originalWarn.apply(console, args);
            addToConsole(args.join(' '), 'warn');
        };

        function checkSystem() {
            const statusDiv = document.getElementById('system-status');
            statusDiv.innerHTML = '<div class="status info">Checking system...</div>';
            
            setTimeout(() => {
                const checks = [
                    { name: 'jQuery Library', check: () => typeof $ !== 'undefined', required: true },
                    { name: 'HTML Structure', check: () => document.querySelector('.casino-container') !== null, required: true },
                    { name: 'CSS Styles', check: () => getComputedStyle(document.body).fontFamily.includes('system'), required: true },
                    { name: 'Game JavaScript', check: () => true, required: true }
                ];
                
                let html = '';
                let allPassed = true;
                
                checks.forEach(check => {
                    try {
                        const passed = check.check();
                        const status = passed ? 'success' : 'error';
                        if (!passed && check.required) allPassed = false;
                        
                        html += `<div class="status ${status}">
                            <strong>${check.name}:</strong> ${passed ? 'PASS' : 'FAIL'}
                        </div>`;
                    } catch (e) {
                        html += `<div class="status error">
                            <strong>${check.name}:</strong> ERROR - ${e.message}
                        </div>`;
                        if (check.required) allPassed = false;
                    }
                });
                
                if (allPassed) {
                    html = '<div class="status success"><strong>All Systems Operational!</strong></div>' + html;
                } else {
                    html = '<div class="status error"><strong>System Issues Detected</strong></div>' + html;
                }
                
                statusDiv.innerHTML = html;
            }, 1000);
        }

        function clearConsole() {
            consoleOutput.innerHTML = 'Console cleared...';
        }

        // Auto-check system on load
        window.addEventListener('load', () => {
            setTimeout(checkSystem, 2000);
        });

        // Log initial message
        console.log('Debug console initialized');
        console.log('Game loading...');
    </script>
</body>
</html>
