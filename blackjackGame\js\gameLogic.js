class BlackjackLogic {
    constructor() {
        this.deck = [];
        this.playerHand = [];
        this.dealerHand = [];
        this.gameState = 'betting'; // betting, dealing, playing, dealerTurn, gameOver
        this.balance = 1000;
        this.currentBet = 0;
        this.gameResult = null;
        this.canDoubleDown = false;
        this.canSplit = false;
        this.stats = {
            gamesPlayed: 0,
            gamesWon: 0,
            blackjacks: 0,
            totalWinnings: 0,
            biggestWin: 0,
            currentStreak: 0,
            longestStreak: 0
        };
        
        this.loadStats();
        this.initializeDeck();
    }

    initializeDeck() {
        this.deck = [];
        const suits = ['hearts', 'diamonds', 'clubs', 'spades'];
        const ranks = ['A', '2', '3', '4', '5', '6', '7', '8', '9', '10', 'J', 'Q', 'K'];
        
        for (let suit of suits) {
            for (let rank of ranks) {
                this.deck.push({ suit, rank });
            }
        }
        
        this.shuffleDeck();
    }

    shuffleDeck() {
        this.deck = Utils.shuffleArray(this.deck);
    }

    dealCard() {
        if (this.deck.length < 10) {
            this.initializeDeck();
        }
        return this.deck.pop();
    }

    getCardValue(card) {
        if (card.rank === 'A') return 11;
        if (['K', 'Q', 'J'].includes(card.rank)) return 10;
        return parseInt(card.rank);
    }

    getHandValue(hand) {
        let value = 0;
        let aces = 0;
        
        for (let card of hand) {
            if (card.rank === 'A') {
                aces++;
                value += 11;
            } else {
                value += this.getCardValue(card);
            }
        }
        
        while (value > 21 && aces > 0) {
            value -= 10;
            aces--;
        }
        
        return value;
    }

    isBlackjack(hand) {
        return hand.length === 2 && this.getHandValue(hand) === 21;
    }

    isBusted(hand) {
        return this.getHandValue(hand) > 21;
    }

    canSplitHand(hand) {
        return hand.length === 2 && this.getCardValue(hand[0]) === this.getCardValue(hand[1]);
    }

    canDoubleDownHand(hand) {
        return hand.length === 2;
    }

    placeBet(amount) {
        if (this.gameState !== 'betting' || this.balance < amount) {
            return false;
        }
        
        this.currentBet += amount;
        this.balance -= amount;
        return true;
    }

    clearBet() {
        if (this.gameState !== 'betting') return false;
        
        this.balance += this.currentBet;
        this.currentBet = 0;
        return true;
    }

    startGame() {
        if (this.gameState !== 'betting' || this.currentBet === 0) {
            return false;
        }
        
        this.gameState = 'dealing';
        this.playerHand = [];
        this.dealerHand = [];
        this.gameResult = null;
        
        // Deal initial cards
        this.playerHand.push(this.dealCard());
        this.dealerHand.push(this.dealCard());
        this.playerHand.push(this.dealCard());
        this.dealerHand.push(this.dealCard());
        
        this.canDoubleDown = this.canDoubleDownHand(this.playerHand) && this.balance >= this.currentBet;
        this.canSplit = this.canSplitHand(this.playerHand) && this.balance >= this.currentBet;
        
        if (this.isBlackjack(this.playerHand)) {
            this.gameState = 'gameOver';
            this.checkGameEnd();
        } else {
            this.gameState = 'playing';
        }
        
        return true;
    }

    hit() {
        if (this.gameState !== 'playing') return false;
        
        this.playerHand.push(this.dealCard());
        this.canDoubleDown = false;
        this.canSplit = false;
        
        if (this.isBusted(this.playerHand)) {
            this.gameState = 'gameOver';
            this.checkGameEnd();
        }
        
        return true;
    }

    stand() {
        if (this.gameState !== 'playing') return false;
        
        this.gameState = 'dealerTurn';
        this.playDealerHand();
        return true;
    }

    doubleDown() {
        if (this.gameState !== 'playing' || !this.canDoubleDown) return false;
        
        this.balance -= this.currentBet;
        this.currentBet *= 2;
        this.canDoubleDown = false;
        this.canSplit = false;
        
        this.playerHand.push(this.dealCard());
        
        if (this.isBusted(this.playerHand)) {
            this.gameState = 'gameOver';
            this.checkGameEnd();
        } else {
            this.gameState = 'dealerTurn';
            this.playDealerHand();
        }
        
        return true;
    }

    playDealerHand() {
        while (this.getHandValue(this.dealerHand) < 17) {
            this.dealerHand.push(this.dealCard());
        }
        
        this.gameState = 'gameOver';
        this.checkGameEnd();
    }

    checkGameEnd() {
        const playerValue = this.getHandValue(this.playerHand);
        const dealerValue = this.getHandValue(this.dealerHand);
        const playerBlackjack = this.isBlackjack(this.playerHand);
        const dealerBlackjack = this.isBlackjack(this.dealerHand);
        
        if (this.isBusted(this.playerHand)) {
            this.gameResult = 'bust';
        } else if (this.isBusted(this.dealerHand)) {
            this.gameResult = 'dealerBust';
        } else if (playerBlackjack && dealerBlackjack) {
            this.gameResult = 'push';
        } else if (playerBlackjack) {
            this.gameResult = 'blackjack';
        } else if (dealerBlackjack) {
            this.gameResult = 'dealerBlackjack';
        } else if (playerValue > dealerValue) {
            this.gameResult = 'win';
        } else if (playerValue < dealerValue) {
            this.gameResult = 'lose';
        } else {
            this.gameResult = 'push';
        }
        
        this.calculateWinnings();
        this.updateStats();
    }

    calculateWinnings() {
        let winnings = 0;
        
        switch (this.gameResult) {
            case 'blackjack':
                winnings = Math.floor(this.currentBet * 2.5);
                break;
            case 'win':
            case 'dealerBust':
                winnings = this.currentBet * 2;
                break;
            case 'push':
                winnings = this.currentBet;
                break;
            case 'bust':
            case 'lose':
            case 'dealerBlackjack':
                winnings = 0;
                break;
        }
        
        this.balance += winnings;
        return winnings - this.currentBet;
    }

    updateStats() {
        this.stats.gamesPlayed++;
        
        const netWinnings = this.calculateWinnings() - this.currentBet;
        
        if (netWinnings > 0) {
            this.stats.gamesWon++;
            this.stats.currentStreak++;
            this.stats.totalWinnings += netWinnings;
            
            if (netWinnings > this.stats.biggestWin) {
                this.stats.biggestWin = netWinnings;
            }
            
            if (this.stats.currentStreak > this.stats.longestStreak) {
                this.stats.longestStreak = this.stats.currentStreak;
            }
            
            if (this.gameResult === 'blackjack') {
                this.stats.blackjacks++;
            }
        } else if (this.gameResult !== 'push') {
            this.stats.currentStreak = 0;
        }
        
        this.saveStats();
    }

    newGame() {
        if (this.balance <= 0) {
            this.balance = 1000;
        }
        
        this.gameState = 'betting';
        this.currentBet = 0;
        this.playerHand = [];
        this.dealerHand = [];
        this.gameResult = null;
        this.canDoubleDown = false;
        this.canSplit = false;
        
        if (this.deck.length < 20) {
            this.initializeDeck();
        }
    }

    getGameStateMessage() {
        switch (this.gameState) {
            case 'betting':
                return 'Place your bet and click Deal Cards';
            case 'dealing':
                return 'Dealing cards...';
            case 'playing':
                return 'Choose your action: Hit, Stand, or Double Down';
            case 'dealerTurn':
                return 'Dealer is playing...';
            case 'gameOver':
                return this.getResultMessage();
            default:
                return 'Welcome to Blackjack!';
        }
    }

    getResultMessage() {
        switch (this.gameResult) {
            case 'blackjack':
                return 'Blackjack! You win!';
            case 'win':
                return 'You win!';
            case 'dealerBust':
                return 'Dealer busts! You win!';
            case 'bust':
                return 'Bust! You lose!';
            case 'lose':
                return 'You lose!';
            case 'dealerBlackjack':
                return 'Dealer has Blackjack! You lose!';
            case 'push':
                return 'Push! It\'s a tie!';
            default:
                return '';
        }
    }

    getWinRate() {
        return this.stats.gamesPlayed > 0 ? 
            (this.stats.gamesWon / this.stats.gamesPlayed * 100).toFixed(1) : 0;
    }

    getBlackjackRate() {
        return this.stats.gamesPlayed > 0 ? 
            (this.stats.blackjacks / this.stats.gamesPlayed * 100).toFixed(1) : 0;
    }

    saveStats() {
        localStorage.setItem('blackjackStats', JSON.stringify(this.stats));
    }

    loadStats() {
        const saved = localStorage.getItem('blackjackStats');
        if (saved) {
            this.stats = { ...this.stats, ...JSON.parse(saved) };
        }
    }

    resetStats() {
        this.stats = {
            gamesPlayed: 0,
            gamesWon: 0,
            blackjacks: 0,
            totalWinnings: 0,
            biggestWin: 0,
            currentStreak: 0,
            longestStreak: 0
        };
        this.saveStats();
    }
}
