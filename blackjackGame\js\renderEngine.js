class RenderEngine {
    constructor(canvas) {
        this.canvas = canvas;
        this.ctx = canvas.getContext('2d');
        this.width = 0;
        this.height = 0;
        this.pixelRatio = Utils.getDevicePixelRatio();
        
        this.layers = new Map();
        this.renderObjects = [];
        this.isRunning = false;
        this.frameCount = 0;
        this.lastFrameTime = 0;
        this.fps = 60;
        this.deltaTime = 0;
        
        this.backgroundGradient = null;
        this.tableTexture = null;
        
        this.setupCanvas();
        this.createLayers();
        this.createBackgroundTextures();
    }

    setupCanvas() {
        const container = this.canvas.parentElement;
        const rect = container.getBoundingClientRect();
        
        this.width = rect.width;
        this.height = rect.height;
        
        this.canvas.width = this.width * this.pixelRatio;
        this.canvas.height = this.height * this.pixelRatio;
        this.canvas.style.width = this.width + 'px';
        this.canvas.style.height = this.height + 'px';
        
        this.ctx.scale(this.pixelRatio, this.pixelRatio);
        this.ctx.imageSmoothingEnabled = true;
        this.ctx.imageSmoothingQuality = 'high';
    }

    createLayers() {
        this.layers.set('background', { zIndex: 0, objects: [] });
        this.layers.set('table', { zIndex: 1, objects: [] });
        this.layers.set('shadows', { zIndex: 2, objects: [] });
        this.layers.set('cards', { zIndex: 3, objects: [] });
        this.layers.set('chips', { zIndex: 4, objects: [] });
        this.layers.set('ui', { zIndex: 5, objects: [] });
        this.layers.set('effects', { zIndex: 6, objects: [] });
        this.layers.set('overlay', { zIndex: 7, objects: [] });
    }

    createBackgroundTextures() {
        this.backgroundGradient = Utils.createRadialGradient(
            this.ctx, 
            this.width / 2, 
            this.height / 2, 
            0, 
            Math.max(this.width, this.height) / 2,
            ['#2d8659', '#1a5f4a', '#0f4c3a']
        );
        
        this.createTableTexture();
    }

    createTableTexture() {
        const canvas = document.createElement('canvas');
        const ctx = canvas.getContext('2d');
        canvas.width = this.width;
        canvas.height = this.height;
        
        const gradient = Utils.createRadialGradient(
            ctx,
            this.width / 2,
            this.height / 2,
            0,
            Math.max(this.width, this.height) / 2,
            ['#2d8659', '#1a5f4a', '#0f4c3a']
        );
        
        ctx.fillStyle = gradient;
        ctx.fillRect(0, 0, this.width, this.height);
        
        this.drawTablePattern(ctx);
        this.drawTableBorder(ctx);
        
        this.tableTexture = canvas;
    }

    drawTablePattern(ctx) {
        ctx.save();
        ctx.globalAlpha = 0.1;
        ctx.strokeStyle = '#ffd700';
        ctx.lineWidth = 2;
        
        const centerX = this.width / 2;
        const centerY = this.height / 2;
        
        for (let i = 1; i <= 5; i++) {
            ctx.beginPath();
            ctx.arc(centerX, centerY, i * 80, 0, Math.PI * 2);
            ctx.stroke();
        }
        
        for (let i = 0; i < 8; i++) {
            const angle = (i * Math.PI * 2) / 8;
            const x1 = centerX + Math.cos(angle) * 100;
            const y1 = centerY + Math.sin(angle) * 100;
            const x2 = centerX + Math.cos(angle) * 400;
            const y2 = centerY + Math.sin(angle) * 400;
            
            ctx.beginPath();
            ctx.moveTo(x1, y1);
            ctx.lineTo(x2, y2);
            ctx.stroke();
        }
        
        ctx.restore();
    }

    drawTableBorder(ctx) {
        ctx.save();
        ctx.strokeStyle = '#ffd700';
        ctx.lineWidth = 4;
        ctx.shadowColor = '#000000';
        ctx.shadowBlur = 10;
        ctx.shadowOffsetX = 0;
        ctx.shadowOffsetY = 0;
        
        Utils.drawRoundedRect(ctx, 10, 10, this.width - 20, this.height - 20, 20);
        ctx.stroke();
        
        ctx.restore();
    }

    addToLayer(layerName, object) {
        if (this.layers.has(layerName)) {
            this.layers.get(layerName).objects.push(object);
        }
    }

    removeFromLayer(layerName, object) {
        if (this.layers.has(layerName)) {
            const layer = this.layers.get(layerName);
            const index = layer.objects.indexOf(object);
            if (index > -1) {
                layer.objects.splice(index, 1);
            }
        }
    }

    clearLayer(layerName) {
        if (this.layers.has(layerName)) {
            this.layers.get(layerName).objects = [];
        }
    }

    start() {
        this.isRunning = true;
        this.lastFrameTime = performance.now();
        this.render();
    }

    stop() {
        this.isRunning = false;
    }

    render() {
        if (!this.isRunning) return;
        
        const currentTime = performance.now();
        this.deltaTime = currentTime - this.lastFrameTime;
        this.lastFrameTime = currentTime;
        
        this.ctx.clearRect(0, 0, this.width, this.height);
        
        this.drawBackground();
        
        const sortedLayers = Array.from(this.layers.entries())
            .sort((a, b) => a[1].zIndex - b[1].zIndex);
        
        for (const [layerName, layer] of sortedLayers) {
            for (const object of layer.objects) {
                if (object && typeof object.render === 'function') {
                    object.render(this.ctx, this.deltaTime);
                } else if (object && typeof object.draw === 'function') {
                    object.draw(this.ctx, this.deltaTime);
                }
            }
        }
        
        this.frameCount++;
        if (this.frameCount % 60 === 0) {
            this.fps = Math.round(1000 / this.deltaTime);
        }
        
        requestAnimationFrame(() => this.render());
    }

    drawBackground() {
        if (this.tableTexture) {
            this.ctx.drawImage(this.tableTexture, 0, 0);
        } else {
            this.ctx.fillStyle = this.backgroundGradient;
            this.ctx.fillRect(0, 0, this.width, this.height);
        }
    }

    resize() {
        this.setupCanvas();
        this.createBackgroundTextures();
    }

    getFPS() {
        return this.fps;
    }

    getDeltaTime() {
        return this.deltaTime;
    }

    getWidth() {
        return this.width;
    }

    getHeight() {
        return this.height;
    }

    getCanvas() {
        return this.canvas;
    }

    getContext() {
        return this.ctx;
    }
}

class GameObject {
    constructor(x = 0, y = 0) {
        this.x = x;
        this.y = y;
        this.width = 0;
        this.height = 0;
        this.rotation = 0;
        this.scaleX = 1;
        this.scaleY = 1;
        this.alpha = 1;
        this.visible = true;
        this.active = true;
        this.zIndex = 0;
    }

    update(deltaTime) {
    }

    render(ctx, deltaTime) {
        if (!this.visible) return;
        
        this.update(deltaTime);
        
        ctx.save();
        ctx.globalAlpha = this.alpha;
        ctx.translate(this.x, this.y);
        ctx.rotate(this.rotation);
        ctx.scale(this.scaleX, this.scaleY);
        
        this.draw(ctx, deltaTime);
        
        ctx.restore();
    }

    draw(ctx, deltaTime) {
    }

    getBounds() {
        return {
            x: this.x,
            y: this.y,
            width: this.width,
            height: this.height
        };
    }

    isPointInside(x, y) {
        return Utils.isPointInRect(x, y, this.getBounds());
    }

    setPosition(x, y) {
        this.x = x;
        this.y = y;
    }

    setScale(scaleX, scaleY = scaleX) {
        this.scaleX = scaleX;
        this.scaleY = scaleY;
    }

    setRotation(rotation) {
        this.rotation = rotation;
    }

    setAlpha(alpha) {
        this.alpha = Utils.clamp(alpha, 0, 1);
    }

    setVisible(visible) {
        this.visible = visible;
    }

    setActive(active) {
        this.active = active;
    }

    destroy() {
        this.active = false;
        this.visible = false;
    }
}
