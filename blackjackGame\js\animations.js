class AnimationManager {
    constructor() {
        this.animations = [];
        this.isRunning = false;
    }

    add(animation) {
        this.animations.push(animation);
        if (!this.isRunning) {
            this.start();
        }
    }

    remove(animation) {
        const index = this.animations.indexOf(animation);
        if (index > -1) {
            this.animations.splice(index, 1);
        }
    }

    start() {
        this.isRunning = true;
        this.update();
    }

    stop() {
        this.isRunning = false;
    }

    update() {
        if (!this.isRunning) return;

        this.animations = this.animations.filter(animation => {
            return animation.update();
        });

        if (this.animations.length === 0) {
            this.isRunning = false;
        } else {
            requestAnimationFrame(() => this.update());
        }
    }

    clear() {
        this.animations = [];
        this.isRunning = false;
    }
}

class Animation {
    constructor(duration = 1000, easing = 'easeOutCubic') {
        this.duration = duration;
        this.easing = easing;
        this.startTime = Date.now();
        this.completed = false;
        this.onComplete = null;
        this.onUpdate = null;
    }

    update() {
        if (this.completed) return false;

        const elapsed = Date.now() - this.startTime;
        const progress = Math.min(elapsed / this.duration, 1);
        const easedProgress = this.applyEasing(progress);

        if (this.onUpdate) {
            this.onUpdate(easedProgress, progress);
        }

        if (progress >= 1) {
            this.completed = true;
            if (this.onComplete) {
                this.onComplete();
            }
            return false;
        }

        return true;
    }

    applyEasing(t) {
        switch (this.easing) {
            case 'linear':
                return t;
            case 'easeInCubic':
                return t * t * t;
            case 'easeOutCubic':
                return Utils.easeOutCubic(t);
            case 'easeInOutCubic':
                return Utils.easeInOutCubic(t);
            case 'easeOutBounce':
                return Utils.easeOutBounce(t);
            case 'easeInQuad':
                return t * t;
            case 'easeOutQuad':
                return 1 - (1 - t) * (1 - t);
            case 'easeInOutQuad':
                return t < 0.5 ? 2 * t * t : 1 - Math.pow(-2 * t + 2, 2) / 2;
            default:
                return Utils.easeOutCubic(t);
        }
    }

    setOnComplete(callback) {
        this.onComplete = callback;
        return this;
    }

    setOnUpdate(callback) {
        this.onUpdate = callback;
        return this;
    }
}

class ParticleSystem extends GameObject {
    constructor(x, y, config = {}) {
        super(x, y);
        this.particles = [];
        this.maxParticles = config.maxParticles || 50;
        this.emissionRate = config.emissionRate || 10;
        this.lifetime = config.lifetime || 2000;
        this.particleLifetime = config.particleLifetime || 1000;
        this.gravity = config.gravity || 0.1;
        this.wind = config.wind || 0;
        this.colors = config.colors || ['#ffd700', '#ffed4e', '#ff6b6b'];
        this.size = config.size || 3;
        this.sizeVariation = config.sizeVariation || 1;
        this.speed = config.speed || 5;
        this.speedVariation = config.speedVariation || 2;
        this.angle = config.angle || 0;
        this.angleVariation = config.angleVariation || Math.PI * 2;
        
        this.emissionTimer = 0;
        this.systemTimer = 0;
        this.isEmitting = true;
    }

    update(deltaTime) {
        super.update(deltaTime);
        
        this.systemTimer += deltaTime;
        
        if (this.isEmitting && this.systemTimer < this.lifetime) {
            this.emissionTimer += deltaTime;
            
            while (this.emissionTimer >= 1000 / this.emissionRate && this.particles.length < this.maxParticles) {
                this.emitParticle();
                this.emissionTimer -= 1000 / this.emissionRate;
            }
        }
        
        this.particles = this.particles.filter(particle => {
            particle.update(deltaTime);
            return particle.life > 0;
        });
        
        if (this.systemTimer >= this.lifetime && this.particles.length === 0) {
            this.active = false;
        }
    }

    emitParticle() {
        const angle = this.angle + Utils.randomBetween(-this.angleVariation / 2, this.angleVariation / 2);
        const speed = this.speed + Utils.randomBetween(-this.speedVariation, this.speedVariation);
        const size = this.size + Utils.randomBetween(-this.sizeVariation, this.sizeVariation);
        const color = this.colors[Math.floor(Math.random() * this.colors.length)];
        
        const particle = new Particle(
            this.x,
            this.y,
            Math.cos(angle) * speed,
            Math.sin(angle) * speed,
            size,
            color,
            this.particleLifetime
        );
        
        particle.gravity = this.gravity;
        particle.wind = this.wind;
        
        this.particles.push(particle);
    }

    draw(ctx, deltaTime) {
        this.particles.forEach(particle => {
            particle.draw(ctx);
        });
    }

    stop() {
        this.isEmitting = false;
    }
}

class Particle {
    constructor(x, y, vx, vy, size, color, lifetime) {
        this.x = x;
        this.y = y;
        this.vx = vx;
        this.vy = vy;
        this.size = size;
        this.color = color;
        this.lifetime = lifetime;
        this.life = 1;
        this.decay = 1 / lifetime * 1000;
        this.gravity = 0;
        this.wind = 0;
        this.rotation = 0;
        this.rotationSpeed = Utils.randomBetween(-0.1, 0.1);
    }

    update(deltaTime) {
        this.x += this.vx * deltaTime / 16.67;
        this.y += this.vy * deltaTime / 16.67;
        
        this.vy += this.gravity * deltaTime / 16.67;
        this.vx += this.wind * deltaTime / 16.67;
        
        this.rotation += this.rotationSpeed * deltaTime / 16.67;
        
        this.life -= this.decay * deltaTime / 16.67;
        this.life = Math.max(0, this.life);
    }

    draw(ctx) {
        if (this.life <= 0) return;
        
        ctx.save();
        ctx.globalAlpha = this.life;
        ctx.fillStyle = this.color;
        ctx.translate(this.x, this.y);
        ctx.rotate(this.rotation);
        
        ctx.beginPath();
        ctx.arc(0, 0, this.size * this.life, 0, Math.PI * 2);
        ctx.fill();
        
        ctx.restore();
    }
}

class WinEffect extends GameObject {
    constructor(x, y, message = 'WIN!') {
        super(x, y);
        this.message = message;
        this.particles = new ParticleSystem(x, y, {
            maxParticles: 100,
            emissionRate: 50,
            lifetime: 3000,
            particleLifetime: 2000,
            colors: ['#ffd700', '#ffed4e', '#ff6b6b', '#4ecdc4', '#45b7d1'],
            size: 5,
            sizeVariation: 3,
            speed: 8,
            speedVariation: 4,
            angleVariation: Math.PI * 2,
            gravity: 0.05
        });
        
        this.textScale = 0;
        this.textAlpha = 1;
        this.duration = 3000;
        this.startTime = Date.now();
    }

    update(deltaTime) {
        super.update(deltaTime);
        
        const elapsed = Date.now() - this.startTime;
        const progress = Math.min(elapsed / this.duration, 1);
        
        if (progress < 0.3) {
            this.textScale = Utils.easeOutBounce(progress / 0.3);
        } else if (progress > 0.7) {
            this.textAlpha = 1 - Utils.easeInCubic((progress - 0.7) / 0.3);
        }
        
        this.particles.update(deltaTime);
        
        if (progress >= 1) {
            this.active = false;
        }
    }

    draw(ctx, deltaTime) {
        this.particles.draw(ctx, deltaTime);
        
        if (this.textAlpha > 0) {
            ctx.save();
            ctx.globalAlpha = this.textAlpha;
            ctx.translate(this.x, this.y);
            ctx.scale(this.textScale, this.textScale);
            
            Utils.drawText(ctx, this.message, 0, 0, {
                font: 'bold 48px Arial',
                fillStyle: '#ffd700',
                strokeStyle: '#000000',
                lineWidth: 3,
                textAlign: 'center',
                textBaseline: 'middle',
                shadow: true,
                shadowColor: 'rgba(0,0,0,0.8)',
                shadowBlur: 10,
                shadowOffsetX: 3,
                shadowOffsetY: 3
            });
            
            ctx.restore();
        }
    }
}

class CardDealAnimation {
    constructor(card, targetX, targetY, delay = 0) {
        this.card = card;
        this.startX = card.x;
        this.startY = card.y;
        this.targetX = targetX;
        this.targetY = targetY;
        this.delay = delay;
        this.duration = 800;
        this.startTime = Date.now() + delay;
        this.completed = false;
        this.started = false;
    }

    update() {
        if (this.completed) return false;
        
        const currentTime = Date.now();
        if (currentTime < this.startTime) return true;
        
        if (!this.started) {
            this.started = true;
        }
        
        const elapsed = currentTime - this.startTime;
        const progress = Math.min(elapsed / this.duration, 1);
        const easedProgress = Utils.easeOutCubic(progress);
        
        this.card.x = this.startX + (this.targetX - this.startX) * easedProgress;
        this.card.y = this.startY + (this.targetY - this.startY) * easedProgress;
        
        const height = 50;
        const parabolicY = -4 * height * progress * (progress - 1);
        this.card.y += parabolicY;
        
        this.card.rotation = progress * Math.PI * 2;
        
        if (progress >= 1) {
            this.card.x = this.targetX;
            this.card.y = this.targetY;
            this.card.rotation = 0;
            this.completed = true;
            return false;
        }
        
        return true;
    }
}

class ChipThrowAnimation {
    constructor(chip, targetX, targetY, delay = 0) {
        this.chip = chip;
        this.startX = chip.x;
        this.startY = chip.y;
        this.targetX = targetX;
        this.targetY = targetY;
        this.delay = delay;
        this.duration = 600;
        this.startTime = Date.now() + delay;
        this.completed = false;
        this.started = false;
    }

    update() {
        if (this.completed) return false;
        
        const currentTime = Date.now();
        if (currentTime < this.startTime) return true;
        
        if (!this.started) {
            this.started = true;
        }
        
        const elapsed = currentTime - this.startTime;
        const progress = Math.min(elapsed / this.duration, 1);
        const easedProgress = Utils.easeOutCubic(progress);
        
        this.chip.x = this.startX + (this.targetX - this.startX) * easedProgress;
        this.chip.y = this.startY + (this.targetY - this.startY) * easedProgress;
        
        const height = 30;
        const parabolicY = -4 * height * progress * (progress - 1);
        this.chip.y += parabolicY;
        
        this.chip.rotation += 0.2;
        
        if (progress >= 1) {
            this.chip.x = this.targetX;
            this.chip.y = this.targetY;
            this.chip.startBounce();
            this.completed = true;
            return false;
        }
        
        return true;
    }
}
